import React, { useState } from "react";
import "./Login.css";
import { useNavigate } from "react-router-dom";
import { Link } from "react-router-dom";
import { auth, db } from "../../../firebase";
const Login = () => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [error, setError] = useState("");
  const navigate = useNavigate();


  const handleLogin = async (e) => {
    e.preventDefault();

    if (!email || !password) {
      setError("Veuillez remplir tous les champs");
      return;
    }

    try {
      // 1. Authenticate user with email and password
      const userCredential = await auth.signInWithEmailAndPassword(email, password);
      const user = userCredential.user;
      console.log(user.email)
      console.log(user.displayName)

      // At this point, authentication was successful, and the email exists.
      // 2. Fetch user coordinates from Firestore using the user's UID
      console.log("UID utilisateur :", user.uid);

      const userDocRef = db.collection("users").doc(user.uid);
      const userDocSnapshot = await userDocRef.get();

      if (userDocSnapshot.exists) {
        const userData = userDocSnapshot.data();
        console.log("Coordonnées de l'utilisateur connecté:");
        console.log("Nom:", userData.name); // Adjust based on your Firestore fields
        console.log("Email:", userData.email);
        console.log("Type:", userData.type);
        // Redirect all users to the Home page
        navigate("/Home");

      } else {
        console.log("Erreur: Les informations de l'utilisateur n'ont pas été trouvées dans Firestore.");
        setError("Erreur lors de la récupération des informations utilisateur.");

      }



    } catch (error) {
      setError("Email ou mot de passe incorrect");
      console.error("Erreur d'authentification :", error.message);
    }
  };


  return (
    <section className="login-container">



      <div className="bg-glass">
        <div className="login-header">
          <img src={'./cable.png'} alt="Logo de la société" className="login-logo" />
          <h2>Connexion</h2>
        </div>

        <form onSubmit={handleLogin}>
          {error && <p className="error-message">{error}</p>}
          <div className="input-group">
            <label htmlFor="email">Email :</label>
            <input
              type="email"
              id="email"
              placeholder="Entrez votre email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
            />
          </div>
          <div className="input-group">
            <label htmlFor="password">Mot de passe :</label>
            <input
              type="password"
              id="password"
              placeholder="Entrez votre mot de passe"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
            />
          </div>
          <button type="submit" className="login-button">
            Se connecter
          </button>
          <p className="signup-text">
            Vous n'avez pas de compte ?{" "}
            <Link to="/SignUp" className="signup-link">
              Créer un compte
            </Link>
          </p>
        </form>
      </div>
    </section>
  );
};

export default Login;
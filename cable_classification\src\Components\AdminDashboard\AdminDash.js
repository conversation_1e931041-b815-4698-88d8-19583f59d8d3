import React, { useState, useEffect } from 'react';
import './AdminDashboard.css';
import { db } from '../../firebase';
import DatasetViewer from './DatasetViewer';
import ModelPerformance from './ModelPerformance';
function AdminDashboard() {
    const [dailyStats, setDailyStats] = useState({});
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState('');
    const [activeTab, setActiveTab] = useState('dashboard');

    useEffect(() => {
        const fetchDailyStats = async () => {
            setLoading(true);
            setError('');
            try {
                const predictionsCollection = db.collection('types_cables');
                const querySnapshot = await predictionsCollection.get();
                const stats = {};
                querySnapshot.forEach((doc) => {
                    stats[doc.id] = doc.data();
                });
                setDailyStats(stats);
            } catch (err) {
                console.error("Erreur lors de la récupération des statistiques de prédiction depuis Firebase:", err);
                setError("Erreur lors du chargement des données de prédiction.");
            } finally {
                setLoading(false);
            }
        };

        fetchDailyStats();
    }, []);

    const formatDate = (dateString) => {
        const parts = dateString.split('-');
        return `${parts[0]}/${parts[1]}/${parts[2]}`;
    };

    if (loading) {
        return <div>Chargement des données de prédiction...</div>;
    }

    if (error) {
        return <div className="error-message">Erreur: {error}</div>;
    }

    return (
        <div className="dashboard-container">
            <aside className="sidebar">
                <div className="logo">Admin Panel</div>
                <nav>
                    <ul>
                        <li>
                            <button
                                className={activeTab === 'dashboard' ? 'active' : ''}
                                onClick={() => setActiveTab('dashboard')}
                            >
                                Tableau de bord
                            </button>
                        </li>
                        <li>
                            <button
                                className={activeTab === 'dataset' ? 'active' : ''}
                                onClick={() => setActiveTab('dataset')}
                            >
                                Dataset
                            </button>
                        </li>
                        <li>
                            <button
                                className={activeTab === 'model' ? 'active' : ''}
                                onClick={() => setActiveTab('model')}
                            >
                                Performance du Modèle
                            </button>
                        </li>
                    </ul>
                </nav>
            </aside>

            <main className="main-content">
                {activeTab === 'dashboard' && (
                    <>
                        <header>
                            <h1>Tableau de bord</h1>
                        </header>

                        <div className="widgets-container">
                            {Object.entries(dailyStats)
                                .sort(([dateA], [dateB]) => new Date(dateB.split('-').reverse().join('-')) - new Date(dateA.split('-').reverse().join('-')))
                                .slice(0, 7) // Afficher les 7 derniers jours
                                .map(([date, stats]) => {
                                    const cable_calage = stats?.cable_calage || 0;
                                    const cable_noo = stats?.cable_noo || 0;
                                    const cable_parfait = stats?.cable_parfait || 0;
                                    const total = cable_calage + cable_noo + cable_parfait;

                                    return (
                                        <div key={date} className="widget">
                                            <h2>{formatDate(date)}</h2>
                                            <p>Câbles Calage: <strong>{cable_calage}</strong></p>
                                            <p>Câbles Noo: <strong>{cable_noo}</strong></p>
                                            <p>Câbles Parfait: <strong>{cable_parfait}</strong></p>
                                            <p>Total: <strong>{total}</strong></p>
                                            {stats.lastUpdated && (
                                                <p className="last-updated">
                                                    Dernière prédiction: {new Date(stats.lastUpdated.toDate()).toLocaleTimeString()}
                                                </p>
                                            )}
                                        </div>
                                    );
                                })}
                            {Object.keys(dailyStats).length === 0 && !loading && (
                                <p className="no-data">Aucune prédiction enregistrée récemment.</p>
                            )}
                        </div>

                        <section className="recent-activity">
                            <h2>Dernières Prédictions</h2>
                            <ul>
                                {Object.entries(dailyStats)
                                    .sort(([dateA, statsA], [dateB, statsB]) => (statsB.lastUpdated?.seconds || 0) - (statsA.lastUpdated?.seconds || 0))
                                    .slice(0, 5) // Afficher les 5 dernières prédictions
                                    .map(([date, stats]) => {
                                        const cable_calage = stats?.cable_calage || 0;
                                        const cable_noo = stats?.cable_noo || 0;
                                        const cable_parfait = stats?.cable_parfait || 0;
                                        const lastUpdated = stats.lastUpdated?.toDate();
                                        let predictionText = '';
                                        if (cable_calage > cable_noo && cable_calage > cable_parfait) {
                                            predictionText = `Détection: Câble Calage`;
                                        } else if (cable_noo > cable_calage && cable_noo > cable_parfait) {
                                            predictionText = `Détection: Câble Noo`;
                                        } else if (cable_parfait > cable_calage && cable_parfait > cable_noo) {
                                            predictionText = `Détection: Câble Parfait`;
                                        } else {
                                            predictionText = `Détections multiples ou aucune détection claire`;
                                        }

                                        return lastUpdated && (
                                            <li key={date + lastUpdated.seconds}>
                                                Le {formatDate(date)} à {lastUpdated.toLocaleTimeString()} - {predictionText}
                                            </li>
                                        );
                                    })}
                                {Object.keys(dailyStats).length === 0 && !loading && (
                                    <li>Aucune prédiction récente.</li>
                                )}
                            </ul>
                        </section>
                    </>
                )}

                {activeTab === 'dataset' && (
                    <DatasetViewer />
                )}

                {activeTab === 'model' && (
                    <ModelPerformance />
                )}
            </main>
        </div>
    );
}

export default AdminDashboard;
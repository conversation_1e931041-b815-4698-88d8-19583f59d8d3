.home-container {
  width: 100%;
  padding: 0;
  margin: 0;
}

.home-layout {
  display: flex;
  min-height: calc(100vh - 60px);
}

.home-sidebar {
  width: 300px;
  background: linear-gradient(to bottom, #2c3e50, #1a252f);
  color: white;
  padding: 2rem;
  box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
  animation: slideInLeft 0.5s ease;
}

.home-main-content {
  flex: 1;
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.app-logo {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 2rem;
  text-align: center;
}

.logo-image {
  width: 80px;
  height: 80px;
  margin-bottom: 1rem;
}

.app-logo h2 {
  font-size: 1.5rem;
  margin: 0;
  color: white;
}

.app-description {
  margin-bottom: 2rem;
}

.app-description h3 {
  font-size: 1.2rem;
  margin-bottom: 1rem;
  color: #3498db;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding-bottom: 0.5rem;
}

.app-description p {
  font-size: 0.95rem;
  line-height: 1.6;
  margin-bottom: 1.5rem;
  color: rgba(255, 255, 255, 0.8);
}

.app-description h4 {
  font-size: 1rem;
  margin-bottom: 1rem;
  color: #3498db;
}

.app-description ul {
  list-style: none;
  padding: 0;
  margin: 0 0 2rem 0;
}

.app-description li {
  display: flex;
  margin-bottom: 1rem;
  align-items: flex-start;
}

.feature-icon {
  font-size: 1.2rem;
  margin-right: 0.75rem;
  flex-shrink: 0;
}

.feature-text {
  font-size: 0.9rem;
  color: rgba(255, 255, 255, 0.8);
  line-height: 1.4;
}

.app-version {
  font-size: 0.8rem;
  color: rgba(255, 255, 255, 0.5);
  margin-top: 2rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: 1rem;
}

.app-version p {
  margin: 0.25rem 0;
}

.home-header {
  text-align: center;
  margin-bottom: 3rem;
  animation: fadeIn 0.6s ease;
}

.home-header h1 {
  font-size: 2.5rem;
  color: var(--primary-blue);
  margin-bottom: 0.5rem;
}

.home-subtitle {
  font-size: 1.2rem;
  color: var(--neutral-gray-600);
  margin-bottom: 1rem;
}

.home-error {
  background-color: var(--error-red-light);
  color: white;
  padding: 1rem;
  border-radius: var(--radius-md);
  margin-bottom: 2rem;
  text-align: center;
}

.home-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
}

.home-card {
  background-color: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  padding: 2rem;
  text-align: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  animation: fadeIn 0.8s ease;
}

.home-card:hover {
  transform: translateY(-10px);
  box-shadow: var(--shadow-lg);
}

.card-icon {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0 auto 1.5rem;
  font-size: 2rem;
  color: white;
}

.classification-icon {
  background: linear-gradient(135deg, #4facfe, #00f2fe);
}

.history-icon {
  background: linear-gradient(135deg, #f093fb, #f5576c);
}

.admin-icon {
  background: linear-gradient(135deg, #43e97b, #38f9d7);
}

.home-card h2 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: var(--neutral-gray-800);
}

.home-card p {
  color: var(--neutral-gray-600);
  margin-bottom: 1.5rem;
  line-height: 1.6;
}

.card-button {
  display: inline-block;
  background-color: var(--primary-blue);
  color: white;
  padding: 0.75rem 1.5rem;
  border-radius: var(--radius-md);
  text-decoration: none;
  font-weight: 500;
  transition: background-color 0.3s ease;
}

.card-button:hover {
  background-color: var(--primary-blue-dark);
}

.home-recent {
  background-color: white;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  padding: 2rem;
  animation: fadeIn 1s ease;
}

.home-recent h2 {
  font-size: 1.5rem;
  margin-bottom: 1.5rem;
  color: var(--neutral-gray-800);
  border-bottom: 1px solid var(--neutral-gray-200);
  padding-bottom: 0.75rem;
}

.recent-predictions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.recent-prediction-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  border-radius: var(--radius-md);
  background-color: var(--neutral-gray-50);
  transition: background-color 0.3s ease;
}

.recent-prediction-item:hover {
  background-color: var(--neutral-gray-100);
}

.prediction-date {
  display: flex;
  flex-direction: column;
}

.date {
  font-weight: 500;
  color: var(--neutral-gray-800);
}

.time {
  font-size: 0.9rem;
  color: var(--neutral-gray-500);
}

.prediction-result {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
}

.prediction-label {
  font-size: 0.9rem;
  color: var(--neutral-gray-500);
}

.prediction-type {
  font-weight: 500;
  color: var(--primary-blue);
}

.view-all-link {
  display: block;
  text-align: center;
  margin-top: 1.5rem;
  color: var(--primary-blue);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.3s ease;
}

.view-all-link:hover {
  color: var(--primary-blue-dark);
  text-decoration: underline;
}

.no-predictions {
  text-align: center;
  padding: 2rem;
  color: var(--neutral-gray-500);
}

.home-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50vh;
  font-size: 1.2rem;
  color: var(--primary-blue);
}

@media (max-width: 1024px) {
  .home-sidebar {
    width: 250px;
    padding: 1.5rem;
  }

  .logo-image {
    width: 60px;
    height: 60px;
  }

  .app-logo h2 {
    font-size: 1.3rem;
  }

  .app-description h3 {
    font-size: 1.1rem;
  }

  .app-description p,
  .feature-text {
    font-size: 0.85rem;
  }
}

@media (max-width: 768px) {
  .home-layout {
    flex-direction: column;
  }

  .home-sidebar {
    width: 100%;
    padding: 1.5rem;
    order: 2;
  }

  .home-main-content {
    order: 1;
    padding: 1.5rem;
  }

  .app-logo {
    flex-direction: row;
    justify-content: center;
    margin-bottom: 1.5rem;
  }

  .logo-image {
    width: 50px;
    height: 50px;
    margin-bottom: 0;
    margin-right: 1rem;
  }

  .app-description {
    margin-bottom: 1rem;
  }

  .app-description ul {
    margin-bottom: 1rem;
  }

  .home-header h1 {
    font-size: 2rem;
  }

  .home-cards {
    grid-template-columns: 1fr;
  }

  .recent-prediction-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .prediction-result {
    align-items: flex-start;
  }
}

@media (max-width: 480px) {
  .home-sidebar,
  .home-main-content {
    padding: 1rem;
  }

  .app-logo h2 {
    font-size: 1.2rem;
  }

  .app-description h3 {
    font-size: 1rem;
  }

  .home-header h1 {
    font-size: 1.8rem;
  }
}

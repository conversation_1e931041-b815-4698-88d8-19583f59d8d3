import React, { useState, useRef, useEffect, useCallback } from 'react';
import styles from './ImageUploader.module.css';
import { db, serverTimestamp } from '../firebase';

// Icons as SVG components
const CameraIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"></path>
    <circle cx="12" cy="13" r="4"></circle>
  </svg>
);

const UploadIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
    <polyline points="17 8 12 3 7 8"></polyline>
    <line x1="12" y1="3" x2="12" y2="15"></line>
  </svg>
);

const ZapIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <polygon points="13 2 3 14 12 14 11 22 21 10 12 10 13 2"></polygon>
  </svg>
);

const XIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <line x1="18" y1="6" x2="6" y2="18"></line>
    <line x1="6" y1="6" x2="18" y2="18"></line>
  </svg>
);

const AlertTriangleIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"></path>
    <line x1="12" y1="9" x2="12" y2="13"></line>
    <line x1="12" y1="17" x2="12.01" y2="17"></line>
  </svg>
);

// Success icon for normal cable
const CheckCircleIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
    <polyline points="22 4 12 14.01 9 11.01"></polyline>
  </svg>
);

// Warning icon for redresse cable
const AlertCircleIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <circle cx="12" cy="12" r="10"></circle>
    <line x1="12" y1="8" x2="12" y2="12"></line>
    <line x1="12" y1="16" x2="12.01" y2="16"></line>
  </svg>
);

// Error icon for defeaut cable
const XCircleIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
    <circle cx="12" cy="12" r="10"></circle>
    <line x1="15" y1="9" x2="9" y2="15"></line>
    <line x1="9" y1="9" x2="15" y2="15"></line>
  </svg>
);

const PREDICTION_CLASSES = {
  'cable defeaut': 'Cable Defeaut',
  'cable normal': 'Cable Normal',
  'cable redresse': 'Cable Redresse',
};

function ImageUploader() {
  // State management
  const [imageState, setImageState] = useState({
    selectedImage: null,
    previewUrl: null,
    fileName: null,
    fileType: null,
    fileSize: null,
  });
  const [predictionState, setPredictionState] = useState({
    result: null,
    isLoading: false,
    error: '',
  });
  const [cameraState, setCameraState] = useState({
    isActive: false,
    isCapturing: false,
    hasPermission: true,
    isRealTimeMode: false,
    processingFrame: false,
  });
  const [modelState, setModelState] = useState({
    availableModels: [],
    selectedModel: 'cnn_model',
    isLoading: true,
  });
  const [animationState, setAnimationState] = useState({
    showImage: false,
    showResult: false
  });

  // Refs
  const fileInputRef = useRef(null);
  const videoRef = useRef(null);
  const canvasRef = useRef(null);
  const streamRef = useRef(null);

  // Camera handling
  const startCamera = useCallback(async () => {
    console.log("Starting camera...");

    // First, set the state to indicate we're trying to start the camera
    // Setting isActive to true first ensures the camera container is rendered
    setCameraState(prev => ({
      ...prev,
      isActive: true,  // Set to true to render the camera container
      hasPermission: true,  // Assume permission until proven otherwise
    }));

    // Clear any previous errors
    setPredictionState(prev => ({ ...prev, error: '' }));

    // Give React time to render the camera container
    await new Promise(resolve => setTimeout(resolve, 300));

    try {
      // Check if mediaDevices is available
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error("getUserMedia is not supported in this browser");
      }

      console.log("Requesting camera access...");

      // Try with simpler constraints first
      const constraints = {
        video: true,
        audio: false
      };

      // Request camera access
      const stream = await navigator.mediaDevices.getUserMedia(constraints);
      console.log("Camera access granted:", stream);

      // Check if video tracks are available
      if (stream.getVideoTracks().length === 0) {
        throw new Error("No video tracks available in the stream");
      }

      // Check if video element is available
      console.log("Video ref check:", videoRef.current ? "Available" : "Not available");

      // Create a small delay to ensure the component has fully rendered
      await new Promise(resolve => setTimeout(resolve, 100));

      // Check again after delay
      console.log("Video ref after delay:", videoRef.current ? "Available" : "Not available");

      if (!videoRef.current) {
        // Instead of throwing an error, let's create the video element dynamically
        console.log("Creating video element dynamically");
        const videoElement = document.createElement('video');
        videoElement.autoplay = true;
        videoElement.playsInline = true;
        videoElement.muted = true;
        videoElement.className = styles.videoPreview;
        videoElement.style.width = '100%';
        videoElement.style.maxWidth = '500px';
        videoElement.style.height = 'auto';

        // Add to the DOM
        const container = document.querySelector(`.${styles.cameraContainer}`);
        if (container) {
          // Insert at the beginning of the container
          container.insertBefore(videoElement, container.firstChild);
          // Update the ref
          videoRef.current = videoElement;
        } else {
          throw new Error("Camera container not found in DOM");
        }
      }

      // Set the stream to the video element
      videoRef.current.srcObject = stream;

      // Add event listeners to debug video element
      videoRef.current.onloadedmetadata = () => {
        console.log("Video metadata loaded");
        videoRef.current.play().then(() => {
          console.log("Video playback started");
        }).catch(e => {
          console.error("Video playback failed:", e);
        });
      };

      videoRef.current.onerror = (e) => {
        console.error("Video element error:", e);
      };

      // Store the stream for later cleanup
      streamRef.current = stream;

      // Update state to indicate camera is active
      console.log("Camera started successfully");
      setCameraState(prev => ({
        ...prev,
        isActive: true,
        hasPermission: true,
        error: ''
      }));

    } catch (err) {
      console.error("Camera access error:", err);

      let errorMessage = "Impossible d'accéder à la caméra: " + err.message;

      if (err.name === 'NotAllowedError' || err.name === 'PermissionDeniedError') {
        errorMessage = "Accès à la caméra refusé. Veuillez autoriser l'accès à la caméra dans les paramètres de votre navigateur.";
        setCameraState(prev => ({ ...prev, hasPermission: false }));
      } else if (err.name === 'NotFoundError' || err.name === 'DevicesNotFoundError') {
        errorMessage = "Aucune caméra détectée sur cet appareil.";
      } else if (err.name === 'NotReadableError' || err.name === 'TrackStartError') {
        errorMessage = "La caméra est peut-être utilisée par une autre application.";
      } else if (err.name === 'AbortError') {
        errorMessage = "L'accès à la caméra a été interrompu.";
      } else if (err.name === 'OverconstrainedError') {
        errorMessage = "Impossible de trouver une caméra correspondant aux contraintes demandées.";
        // Try again with simpler constraints
        console.log("Retrying with simpler constraints...");
        return startCamera();
      }

      setPredictionState(prev => ({ ...prev, error: errorMessage }));
      // Keep the camera container visible but show the error
      // This way the user can try again without having to click the button again
      setCameraState(prev => ({
        ...prev,
        // Keep isActive true so the container stays visible
        isActive: true,
        // But mark that we had an error
        hasError: true
      }));
    }
  }, []);

  const stopCamera = useCallback(() => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
    }
    setCameraState(prev => ({
      ...prev,
      isActive: false,
      isRealTimeMode: false
    }));
  }, []);

  // Process a frame from the video stream for real-time classification
  const processVideoFrame = useCallback(async () => {
    if (!cameraState.isRealTimeMode || !videoRef.current || cameraState.processingFrame) {
      return;
    }

    setCameraState(prev => ({ ...prev, processingFrame: true }));

    try {
      const canvas = canvasRef.current;
      const video = videoRef.current;

      if (!canvas || !video) {
        throw new Error("Canvas or video element not available");
      }

      // Set canvas dimensions to match video
      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;

      // Draw the video frame to the canvas
      const ctx = canvas.getContext('2d');
      ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

      // Convert to JPEG blob (0.8 quality for faster processing)
      canvas.toBlob(async (blob) => {
        try {
          if (!blob) {
            throw new Error("Failed to create image blob");
          }

          // Create file object from blob
          const file = new File([blob], "realtime-frame.jpg", { type: "image/jpeg" });

          // Send the frame for classification
          await classifyFrame(file);

        } catch (error) {
          console.error("Error processing frame:", error);
          setPredictionState(prev => ({
            ...prev,
            error: "Erreur lors du traitement de l'image: " + error.message
          }));
        } finally {
          // Allow processing the next frame
          setCameraState(prev => ({ ...prev, processingFrame: false }));
        }
      }, 'image/jpeg', 0.8);
    } catch (error) {
      console.error("Error capturing frame:", error);
      setCameraState(prev => ({ ...prev, processingFrame: false }));
    }
  }, [cameraState.isRealTimeMode, cameraState.processingFrame]);

  // Classify a single frame
  const classifyFrame = useCallback(async (imageFile) => {
    if (!imageFile) return;

    const formData = new FormData();
    formData.append('image', imageFile);
    formData.append('model', 'resnet50');

    try {
      const endpoint = 'http://localhost:5000/api/classify';

      const response = await fetch(endpoint, {
        method: 'POST',
        body: formData,
      });

      if (response.ok) {
        const data = await response.json();

        // Process the response
        const { predictions, top_prediction, metadata } = data;

        const allPredictions = predictions.map(pred => ({
          ...pred,
          friendlyName: PREDICTION_CLASSES[pred.class] || pred.class
        }));

        const topClass = top_prediction.class;
        const confidence = top_prediction.probability;

        // Create result object
        let resultWithPrediction = {};

        // Add metadata to result object
        if (metadata) {
          resultWithPrediction = {
            modelInfo: {
              name: metadata.model_name,
              inferenceTime: metadata.inference_time,
              modelType: 'resnet50'
            }
          };
        }

        // Save to state
        setPredictionState(prev => ({
          ...prev,
          result: {
            ...resultWithPrediction,
            topClass,
            confidence,
            predictions: allPredictions,
            timestamp: new Date(),
          },
          isLoading: false
        }));

        // Don't save to Firestore for real-time mode to avoid flooding the database
      } else {
        console.error(`Backend error: ${response.status}`);
      }
    } catch (error) {
      console.error("Error classifying frame:", error);
    }
  }, []);

  const captureImage = useCallback(() => {
    setPredictionState(prev => ({ ...prev, error: '' }));
    setCameraState(prev => ({ ...prev, isCapturing: true }));

    const canvas = canvasRef.current;
    const video = videoRef.current;

    if (canvas && video) {
      try {
        // Set canvas dimensions to match video
        canvas.width = video.videoWidth;
        canvas.height = video.videoHeight;

        // Draw the video frame to the canvas
        const ctx = canvas.getContext('2d');
        ctx.drawImage(video, 0, 0, canvas.width, canvas.height);

        // Convert to high-quality JPEG blob (0.95 quality)
        canvas.toBlob(blob => {
          try {
            if (!blob) {
              throw new Error("Failed to create image blob");
            }

            // Log blob size for debugging
            console.log(`Captured image size: ${Math.round(blob.size / 1024)} KB`);

            // Create file object from blob
            const file = new File([blob], "camera-capture.jpg", { type: "image/jpeg" });

            // Create object URL and set as selected image
            const imageUrl = URL.createObjectURL(blob);

            setImageState({
              selectedImage: file,
              previewUrl: imageUrl,
              fileName: "camera-capture.jpg",
              fileType: "image/jpeg",
              fileSize: blob.size,
            });

            setAnimationState(prev => ({ ...prev, showImage: true }));

            canvasRef.current.blob = blob;
            setCameraState(prev => ({ ...prev, isCapturing: false }));

            // Automatically stop camera after successful capture
            stopCamera();
          } catch (blobError) {
            console.error("Error processing captured image:", blobError);
            setPredictionState(prev => ({
              ...prev,
              error: "Erreur lors du traitement de l'image capturée: " + blobError.message
            }));
            setCameraState(prev => ({ ...prev, isCapturing: false }));
          }
        }, 'image/jpeg', 0.95);
      } catch (captureError) {
        console.error("Error capturing image:", captureError);
        setPredictionState(prev => ({
          ...prev,
          error: "Erreur lors de la capture de l'image: " + captureError.message
        }));
        setCameraState(prev => ({ ...prev, isCapturing: false }));
      }
    } else {
      setPredictionState(prev => ({ ...prev, error: "Caméra non disponible" }));
      setCameraState(prev => ({ ...prev, isCapturing: false }));
    }
  }, [stopCamera]);

  // File handling
  const handleImageChange = useCallback((event) => {
    const file = event.target.files[0];
    if (file) {
      // Create preview URL
      const previewUrl = URL.createObjectURL(file);

      setImageState({
        selectedImage: file,
        previewUrl,
        fileName: file.name,
        fileType: file.type,
        fileSize: file.size,
      });

      setAnimationState(prev => ({ ...prev, showImage: true }));

      // Clear previous results
      setPredictionState(prev => ({
        ...prev,
        result: null,
        error: ''
      }));

      // Ensure camera is off
      if (cameraState.isActive) {
        stopCamera();
      }
    }
  }, [cameraState.isActive, stopCamera]);

  const handleUploadButtonClick = () => {
    fileInputRef.current.click();
  };

  const clearImage = useCallback(() => {
    // Revoke object URL to prevent memory leaks
    if (imageState.previewUrl) {
      URL.revokeObjectURL(imageState.previewUrl);
    }

    setImageState({
      selectedImage: null,
      previewUrl: null,
      fileName: null,
      fileType: null,
      fileSize: null,
    });

    setPredictionState(prev => ({
      ...prev,
      result: null,
      error: ''
    }));

    setAnimationState(prev => ({
      ...prev,
      showImage: false,
      showResult: false
    }));
  }, [imageState.previewUrl]);

  // Prediction handling
  const handlePredictClick = useCallback(async () => {
    if (!imageState.selectedImage) {
      setPredictionState(prev => ({
        ...prev,
        error: 'Veuillez sélectionner une image ou capturer depuis la caméra.',
        result: null
      }));
      return;
    }

    setPredictionState(prev => ({
      ...prev,
      error: '',
      isLoading: true
    }));

    const formData = new FormData();
    formData.append('image', imageState.selectedImage);

    // Always use ResNet50 model
    formData.append('model', 'resnet50');

    try {
      // Always use the ResNet50 endpoint
      const endpoint = 'http://localhost:5000/api/classify';

      const response = await fetch(endpoint, {
        method: 'POST',
        body: formData,
      });

      if (response.ok) {
        const data = await response.json();

        let resultWithPrediction = '';
        let confidence = 0;
        let topClass = '';
        let allPredictions = [];

        // Always use ResNet50 model response format
        const { predictions, top_prediction, metadata } = data;

        allPredictions = predictions.map(pred => ({
          ...pred,
          friendlyName: PREDICTION_CLASSES[pred.class] || pred.class
        }));

        topClass = top_prediction.class;
        confidence = top_prediction.probability;

        // Add metadata to result object
        if (metadata) {
          resultWithPrediction = {
            modelInfo: {
              name: metadata.model_name,
              inferenceTime: metadata.inference_time,
              modelType: 'resnet50'
            }
          };
        }

        // Save to state
        setPredictionState(prev => ({
          ...prev,
          result: {
            ...resultWithPrediction,
            topClass,
            confidence,
            predictions: allPredictions,
            timestamp: new Date(),
          },
          isLoading: false
        }));

        setAnimationState(prev => ({
          ...prev,
          showResult: true
        }));

        // Save the prediction to Firestore
        await savePredictionToFirestore(topClass);
      } else {
        setPredictionState(prev => ({
          ...prev,
          error: `Erreur du backend: ${response.status}`,
          isLoading: false
        }));
      }
    } catch (error) {
      setPredictionState(prev => ({
        ...prev,
        error: "Erreur lors de l'envoi de l'image: " + error.message,
        isLoading: false
      }));
    }
  }, [imageState.selectedImage, modelState.selectedModel]);

  // Firestore handling
  async function savePredictionToFirestore(predictedClass) {
    try {
      const today = new Date();
      const dateStr = today.toLocaleDateString("fr-FR").replace(/\//g, "-");

      const docRef = db.collection("types_cables").doc(dateStr);

      // Check if we're online before trying to get the document
      const isOnline = window.navigator.onLine;

      if (!isOnline) {
        console.log("Device is offline, saving prediction locally");
        // Create a new document with default values
        const newDoc = {
          'cable defeaut': predictedClass === 'cable defeaut' ? 1 : 0,
          'cable normal': predictedClass === 'cable normal' ? 1 : 0,
          'cable redresse': predictedClass === 'cable redresse' ? 1 : 0,
          date: dateStr,
          lastUpdated: new Date(), // Use local date when offline
          _isOffline: true // Mark as created offline
        };

        // This will be saved in the local cache due to persistence
        await docRef.set(newDoc, { merge: true });
        console.log("Prediction saved to local cache");
        return;
      }

      // If online, proceed normally
      try {
        const docSnap = await docRef.get();

        const defaultCounts = {
          'cable defeaut': 0,
          'cable normal': 0,
          'cable redresse': 0,
        };

        if (docSnap.exists) {
          const data = docSnap.data() || defaultCounts;
          // Make sure we're using the new class names
          data[predictedClass] = (data[predictedClass] || 0) + 1;
          await docRef.update({
            ...data,
            lastUpdated: serverTimestamp(),
          });
        } else {
          const newDoc = {
            ...defaultCounts,
            [predictedClass]: 1,
            date: dateStr,
            lastUpdated: serverTimestamp(),
          };
          await docRef.set(newDoc);
        }

        console.log("Successfully saved prediction to Firestore");
      } catch (docError) {
        console.error("Error accessing Firestore document:", docError);

        // If there's an error getting the document, create a new one
        const newDoc = {
          'cable defeaut': predictedClass === 'cable defeaut' ? 1 : 0,
          'cable normal': predictedClass === 'cable normal' ? 1 : 0,
          'cable redresse': predictedClass === 'cable redresse' ? 1 : 0,
          date: dateStr,
          lastUpdated: new Date(), // Use local date as fallback
          _isErrorRecovery: true
        };

        await docRef.set(newDoc, { merge: true });
        console.log("Created new document as fallback");
      }
    } catch (error) {
      console.error("Error saving to Firestore:", error);
      // Don't show this error to the user, as the prediction still worked
    }
  }

  // Handle detect and classify
  const handleDetectAndClassify = useCallback(async () => {
    if (!imageState.selectedImage) {
      setPredictionState(prev => ({
        ...prev,
        error: 'Veuillez sélectionner une image ou capturer depuis la caméra.',
        result: null
      }));
      return;
    }

    setPredictionState(prev => ({
      ...prev,
      error: '',
      isLoading: true
    }));

    const formData = new FormData();
    formData.append('image', imageState.selectedImage);

    try {
      // Utiliser le nouvel endpoint de détection et classification
      const endpoint = 'http://localhost:5000/api/detect_and_classify';

      const response = await fetch(endpoint, {
        method: 'POST',
        body: formData,
      });

      if (response.ok) {
        const data = await response.json();

        // Traiter les résultats de détection
        if (data.results && data.results.length > 0) {
          // Prendre le résultat avec la plus haute confiance de classification
          const bestResult = data.results.reduce((prev, current) => {
            const prevConf = prev.classification.top_prediction.probability;
            const currConf = current.classification.top_prediction.probability;
            return prevConf > currConf ? prev : current;
          });

          const topClass = bestResult.classification.top_prediction.class;
          const confidence = bestResult.classification.top_prediction.probability;
          const predictions = bestResult.classification.predictions.map(pred => ({
            ...pred,
            friendlyName: PREDICTION_CLASSES[pred.class] || pred.class
          }));

          // Mettre à jour l'état avec les résultats
          setPredictionState(prev => ({
            ...prev,
            result: {
              topClass,
              confidence,
              predictions,
              timestamp: new Date(),
              annotatedImage: data.annotated_image,
              numDetections: data.num_detections,
              detectionResults: data.results
            },
            isLoading: false
          }));

          // Sauvegarder la prédiction dans Firestore
          await savePredictionToFirestore(topClass);
        } else {
          setPredictionState(prev => ({
            ...prev,
            error: 'Aucun câble détecté dans l\'image',
            isLoading: false
          }));
        }
      } else {
        // Gérer les erreurs HTTP
        if (response.status === 404) {
          setPredictionState(prev => ({
            ...prev,
            error: 'Aucun câble détecté dans l\'image. Essayez une autre image ou un angle différent.',
            isLoading: false
          }));
        } else if (response.status === 501) {
          setPredictionState(prev => ({
            ...prev,
            error: 'La détection de câbles n\'est pas disponible sur le serveur.',
            isLoading: false
          }));
        } else {
          setPredictionState(prev => ({
            ...prev,
            error: `Erreur du backend: ${response.status}`,
            isLoading: false
          }));
        }
      }
    } catch (error) {
      setPredictionState(prev => ({
        ...prev,
        error: "Erreur lors de l'envoi de l'image: " + error.message,
        isLoading: false
      }));
    }
  }, [imageState.selectedImage, savePredictionToFirestore]);

  // Fetch available models when component mounts
  useEffect(() => {
    const fetchModels = async () => {
      try {
        const response = await fetch('http://localhost:5000/api/models');
        if (response.ok) {
          const data = await response.json();
          setModelState(prev => ({
            ...prev,
            availableModels: data.models,
            selectedModel: data.default_model || 'cnn_model',
            isLoading: false
          }));
        } else {
          console.error('Failed to fetch models');
          setModelState(prev => ({
            ...prev,
            isLoading: false
          }));
        }
      } catch (error) {
        console.error('Error fetching models:', error);
        setModelState(prev => ({
          ...prev,
          isLoading: false
        }));
      }
    };

    fetchModels();

    // Clean up on unmount
    return () => {
      if (imageState.previewUrl) {
        URL.revokeObjectURL(imageState.previewUrl);
      }
      stopCamera();
    };
  }, [stopCamera, imageState.previewUrl]);

  // Real-time frame processing
  useEffect(() => {
    let frameProcessingInterval = null;

    if (cameraState.isRealTimeMode && cameraState.isActive) {
      // Process frames at regular intervals (every 500ms)
      frameProcessingInterval = setInterval(() => {
        processVideoFrame();
      }, 500); // Adjust this value to control processing frequency

      console.log("Started real-time frame processing");
    }

    return () => {
      if (frameProcessingInterval) {
        clearInterval(frameProcessingInterval);
        console.log("Stopped real-time frame processing");
      }
    };
  }, [cameraState.isRealTimeMode, cameraState.isActive, processVideoFrame]);

  // Keyboard shortcuts
  useEffect(() => {
    const handleKeyDown = (e) => {
      // Escape key to cancel camera
      if (e.key === 'Escape' && cameraState.isActive) {
        stopCamera();
      }

      // Space key to capture when camera is active and not in real-time mode
      if (e.key === ' ' && cameraState.isActive && !cameraState.isCapturing && !cameraState.isRealTimeMode) {
        captureImage();
      }

      // 'R' key to toggle real-time mode
      if (e.key === 'r' && cameraState.isActive) {
        setCameraState(prev => ({ ...prev, isRealTimeMode: !prev.isRealTimeMode }));
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, [cameraState.isActive, cameraState.isCapturing, cameraState.isRealTimeMode, captureImage, stopCamera]);

  // Calculate result status for styling
  const getResultStatus = () => {
    if (!predictionState.result) return null;

    const { confidence, topClass } = predictionState.result;

    if (topClass === 'cable normal') return 'success';
    if (topClass === 'cable redresse') return 'warning';
    if (topClass === 'cable defeaut') return 'error';

    // Default based on confidence
    if (confidence > 0.8) return 'success';
    if (confidence > 0.5) return 'warning';
    return 'error';
  };

  const resultStatus = getResultStatus();

  const getModelDisplayName = (modelName) => {
    if (modelName === 'cnn_model') return 'Modèle CNN Original';
    if (modelName === 'resnet50') return 'ResNet50 Fine-tuned';
    if (modelName.includes('efficientnetb3')) return 'EfficientNet B3';
    if (modelName.includes('efficientnetb4')) return 'EfficientNet B4';
    return modelName;
  };

  const formatFileSize = (size) => {
    if (!size) return '';

    const kb = size / 1024;
    if (kb < 1000) return `${Math.round(kb)} KB`;
    return `${(kb / 1024).toFixed(2)} MB`;
  };

  return (
    <div className={styles.container}>
      <div className={styles.uploadSection}>
        <div className={styles.uploadButtons}>
          <button
            className={styles.uploadButton}
            onClick={handleUploadButtonClick}
            disabled={cameraState.isActive}
          >
            <span className={styles.buttonIcon}><UploadIcon /></span>
            Sélectionner une image
          </button>

          <button
            className={`${styles.cameraButton} ${cameraState.isActive ? styles.active : ''}`}
            onClick={() => {
              console.log("Camera button clicked, current state:", cameraState.isActive);
              if (cameraState.isActive) {
                stopCamera();
              } else {
                startCamera();
              }
            }}
            disabled={!cameraState.hasPermission && !cameraState.isActive}
          >
            <span className={styles.buttonIcon}><CameraIcon /></span>
            {cameraState.isActive ? 'Arrêter la caméra' : 'Utiliser la caméra'}
          </button>
            {/* ✅ Add this missing input element */}
  <input
    type="file"
    accept="image/*"
    style={{ display: 'none' }}
    ref={fileInputRef}
    onChange={handleImageChange}
  />
        </div>

        {cameraState.isActive && (
          <div className={styles.cameraContainer}>
            {cameraState.isRealTimeMode && (
              <div className={styles.realTimeStatus}>
                <span className={styles.recordingIndicator}></span>
                Flux en temps réel
              </div>
            )}
            <video
              ref={videoRef}
              autoPlay
              playsInline
              muted
              className={styles.videoPreview}
              style={{ width: '100%', maxWidth: '500px', height: 'auto' }}
              onCanPlay={() => console.log("Video can play now")}
            ></video>

            {predictionState.error ? (
              // Show retry button if there's an error
              <button
                onClick={() => {
                  // Clear error and try again
                  setPredictionState(prev => ({ ...prev, error: '' }));
                  // Stop any existing streams
                  if (streamRef.current) {
                    streamRef.current.getTracks().forEach(track => track.stop());
                  }
                  // Try again with a small delay
                  setTimeout(() => startCamera(), 500);
                }}
                className={`${styles.captureButton} ${styles.retryButton}`}
              >
                Réessayer
              </button>
            ) : (
              // Show camera control buttons
              <div className={styles.cameraControls}>
                {/* Real-time mode toggle button */}
                <button
                  onClick={() => setCameraState(prev => ({
                    ...prev,
                    isRealTimeMode: !prev.isRealTimeMode,
                    // Reset any previous results when toggling modes
                    ...(prev.isRealTimeMode ? {} : { processingFrame: false })
                  }))}
                  className={`${styles.realTimeButton} ${cameraState.isRealTimeMode ? styles.active : ''}`}
                >
                  {cameraState.isRealTimeMode ? (
                    <>
                      <span className={styles.recordingIndicator}></span>
                      Flux en direct 🔴
                    </>
                  ) : 'Activer flux en direct 📡'}
                </button>

                {/* Only show capture button when not in real-time mode */}
                {!cameraState.isRealTimeMode && (
                  <button
                    onClick={captureImage}
                    className={styles.captureButton}
                    disabled={cameraState.isCapturing}
                  >
                    {cameraState.isCapturing ? 'Capture en cours...' : 'Capturer'}
                  </button>
                )}
              </div>
            )}

            {cameraState.isCapturing && (
              <div className={styles.loadingOverlay}>
                <div className={styles.loadingSpinner}></div>
              </div>
            )}
          </div>
        )}

        {imageState.previewUrl && (
          <div className={`${styles.imageContainer} ${styles.hasImage}`}>
            <img
              src={imageState.previewUrl}
              alt="Image sélectionnée"
              className={styles.uploadedImage}
            />

            <button
              className={styles.clearImageButton}
              onClick={clearImage}
            >
              <XIcon />
            </button>

            {imageState.fileName && (
              <div className={styles.imageInfo}>
                <span title={imageState.fileName}>{imageState.fileName.length > 20
                  ? imageState.fileName.substring(0, 17) + '...'
                  : imageState.fileName}</span>
                <span>{formatFileSize(imageState.fileSize)}</span>
              </div>
            )}
          </div>
        )}

        <div className={styles.modelSelector}>
          <label htmlFor="model-select">Modèle:</label>
          <div className={styles.modelFixed}>
            ResNet50 Fine-tuned
          </div>
          <input type="hidden" id="model-select" value="resnet50" />
        </div>

        <div className={styles.actionButtons}>
          <button
            className={`${styles.predictButton} ${predictionState.isLoading ? styles.loading : ''}`}
            onClick={handlePredictClick}
            disabled={!imageState.selectedImage || predictionState.isLoading}
          >
            <span className={styles.buttonIcon}><ZapIcon /></span>
            {predictionState.isLoading ? 'Analyse en cours...' : 'Prédire'}
          </button>

          <button
            className={`${styles.detectButton} ${predictionState.isLoading ? styles.loading : ''}`}
            onClick={handleDetectAndClassify}
            disabled={!imageState.selectedImage || predictionState.isLoading}
          >
            <span className={styles.buttonIcon}>🔍</span>
            {predictionState.isLoading ? 'Analyse en cours...' : 'Détecter & Classifier'}
          </button>
        </div>
      </div>

      <canvas ref={canvasRef} style={{ display: 'none' }} />

      {predictionState.error && (
        <div className={styles.error}>
          <AlertTriangleIcon />
          <p>{predictionState.error}</p>
        </div>
      )}

      {predictionState.result && (
        <div className={`${styles.resultContainer} ${resultStatus ? styles[resultStatus] : ''} ${cameraState.isRealTimeMode ? styles.realTimeResult : ''}`}>
          <h2 className={styles.resultTitle}>
            {cameraState.isRealTimeMode ? 'Analyse en Temps Réel' : 'Résultat de la Prédiction'}
            {cameraState.isRealTimeMode && <span className={styles.liveIndicator}>LIVE</span>}
            {predictionState.result.numDetections && (
              <span className={styles.detectionBadge}>
                {predictionState.result.numDetections} {predictionState.result.numDetections > 1 ? 'câbles détectés' : 'câble détecté'}
              </span>
            )}
          </h2>

          {/* Afficher l'image annotée si disponible */}
          {predictionState.result.annotatedImage && (
            <div className={styles.annotatedImageContainer}>
              <img
                src={predictionState.result.annotatedImage}
                alt="Image annotée avec détection de câbles"
                className={styles.annotatedImage}
              />
            </div>
          )}

          <div className={styles.resultContent}>
            <div className={styles.resultMain}>
              <div className={styles.resultTopClass}>
                <div className={styles.resultIcon}>
                  {resultStatus === 'success' && <CheckCircleIcon />}
                  {resultStatus === 'warning' && <AlertCircleIcon />}
                  {resultStatus === 'error' && <XCircleIcon />}
                </div>
                <h3>
                  {PREDICTION_CLASSES[predictionState.result.topClass] ||
                    predictionState.result.topClass}
                </h3>
                <div className={`${styles.confidenceBadge} ${styles[resultStatus]}`}>
                  {(predictionState.result.confidence * 100).toFixed(1)}%
                </div>
              </div>

              {predictionState.result.modelInfo && (
                <div className={styles.resultMeta}>
                  <p>Modèle: {getModelDisplayName(predictionState.result.modelInfo.name)}</p>
                  <p>Type: {predictionState.result.modelInfo.modelType === 'resnet50' ? 'ResNet50' :
                           predictionState.result.modelInfo.modelType === 'efficientnet' ? 'EfficientNet' :
                           'CNN'}</p>
                  <p>Temps d'inférence: {(predictionState.result.modelInfo.inferenceTime * 1000).toFixed(2)} ms</p>
                </div>
              )}

              <div className={styles.timestamp}>
                {predictionState.result.timestamp.toLocaleTimeString()}
              </div>
            </div>

            <div className={styles.predictionsDetail}>
              <h4>Probabilités par classe</h4>
              {predictionState.result.predictions.map((pred, index) => (
                <div key={index} className={styles.predictionItem}>
                  <div className={styles.predictionName}>
                    {pred.friendlyName}
                  </div>
                  <div className={styles.predictionBarContainer}>
                    <div
                      className={`${styles.predictionBar} ${
                        pred.class === predictionState.result.topClass
                          ? styles.topPrediction
                          : ''
                      }`}
                      style={{ width: `${pred.probability * 100}%` }}
                    />
                  </div>
                  <div className={styles.predictionValue}>
                    {(pred.probability * 100).toFixed(1)}%
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export default ImageUploader;
/* Enhanced Login.css - 10x Better */
:root {
  /* Color Palette */
  --login-primary: #3b82f6;
  --login-primary-dark: #1d4ed8;
  --login-primary-light: #60a5fa;
  --login-secondary: #8b5cf6;
  --login-accent: #06b6d4;
  --login-success: #10b981;
  --login-warning: #f59e0b;
  --login-error: #ef4444;
  
  /* Gradients */
  --login-gradient-main: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --login-gradient-glass: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  --login-gradient-error: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
  --login-gradient-success: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
  
  /* Neutral Colors */
  --login-white: #ffffff;
  --login-gray-50: #f9fafb;
  --login-gray-100: #f3f4f6;
  --login-gray-200: #e5e7eb;
  --login-gray-300: #d1d5db;
  --login-gray-400: #9ca3af;
  --login-gray-500: #6b7280;
  --login-gray-600: #4b5563;
  --login-gray-700: #374151;
  --login-gray-800: #1f2937;
  --login-gray-900: #111827;
  
  /* Shadows */
  --login-shadow-subtle: 0 1px 2px rgba(0, 0, 0, 0.05);
  --login-shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  --login-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --login-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --login-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --login-shadow-glow: 0 0 40px rgba(59, 130, 246, 0.3);
  
  /* Transitions */
  --login-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --login-bounce: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.login-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: var(--login-gradient-main);
  background-size: 400% 400%;
  animation: gradientShift 8s ease infinite;
  padding: 2rem;
  position: relative;
  overflow: hidden;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.login-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 30%),
    radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 30%),
    radial-gradient(circle at 40% 60%, rgba(139, 92, 246, 0.2) 0%, transparent 40%);
  animation: particlesFloat 20s ease-in-out infinite;
  z-index: 1;
}

@keyframes particlesFloat {
  0%, 100% { transform: translateY(0) rotate(0deg); }
  33% { transform: translateY(-20px) rotate(1deg); }
  66% { transform: translateY(10px) rotate(-1deg); }
}

.steg-slogan {
  color: var(--login-white);
  text-align: center;
  margin-bottom: 3rem;
  font-size: clamp(1.5rem, 4vw, 2.5rem);
  font-weight: 800;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  z-index: 10;
  position: relative;
  animation: sloganFloat 3s ease-in-out infinite;
}

@keyframes sloganFloat {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}

.steg-slogan h1 {
  font-size: inherit;
  margin: 0;
  background: linear-gradient(135deg, #ffffff 0%, #bfdbfe 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
}

/* Floating shapes */
.login-container .radius-shape-1,
.login-container .radius-shape-2 {
  position: absolute;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.3), rgba(139, 92, 246, 0.3));
  opacity: 0.6;
  border-radius: 50%;
  animation: floatingShapes 6s ease-in-out infinite;
  z-index: 1;
}

.login-container .radius-shape-1 {
  height: 200px;
  width: 200px;
  top: -50px;
  left: -100px;
  animation-delay: 0s;
}

.login-container .radius-shape-2 {
  width: 300px;
  height: 300px;
  bottom: -50px;
  right: -100px;
  border-radius: 38% 62% 63% 37% / 70% 33% 67% 30%;
  animation-delay: -3s;
}

@keyframes floatingShapes {
  0%, 100% { transform: translate(0, 0) rotate(0deg); }
  25% { transform: translate(20px, -20px) rotate(5deg); }
  50% { transform: translate(-10px, 10px) rotate(-3deg); }
  75% { transform: translate(15px, 5px) rotate(2deg); }
}

.bg-glass {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(25px) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 3rem;
  border-radius: 24px;
  box-shadow: var(--login-shadow-xl);
  text-align: center;
  max-width: 450px;
  width: 100%;
  position: relative;
  z-index: 10;
  animation: cardSlideUp 0.8s ease-out;
  overflow: hidden;
}

@keyframes cardSlideUp {
  from {
    opacity: 0;
    transform: translateY(50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.bg-glass::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--login-gradient-main);
  border-radius: 24px 24px 0 0;
  animation: topBorderGlow 2s ease-in-out infinite;
}

@keyframes topBorderGlow {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 1; }
}

.login-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1.5rem;
  margin-bottom: 2.5rem;
}

.login-logo {
  max-width: 120px;
  height: auto;
  border-radius: 20px;
  box-shadow: var(--login-shadow-lg);
  transition: var(--login-transition);
  animation: logoFloat 3s ease-in-out infinite;
}

@keyframes logoFloat {
  0%, 100% { transform: translateY(0) scale(1); }
  50% { transform: translateY(-5px) scale(1.05); }
}

.login-logo:hover {
  transform: scale(1.1) rotate(5deg);
  box-shadow: var(--login-shadow-xl), var(--login-shadow-glow);
}

.login-header h2 {
  font-size: 2.25rem;
  font-weight: 900;
  background: var(--login-gradient-main);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin: 0;
  position: relative;
}

.login-header h2::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: var(--login-gradient-main);
  border-radius: 2px;
  animation: underlineGrow 1s ease-out 0.5s both;
}

@keyframes underlineGrow {
  from { width: 0; }
  to { width: 60px; }
}

form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.input-group {
  display: flex;
  flex-direction: column;
  text-align: left;
  position: relative;
}

form label {
  font-size: 1rem;
  font-weight: 600;
  color: var(--login-gray-700);
  margin-bottom: 0.5rem;
  transition: var(--login-transition);
}

form input {
  padding: 1rem 1.25rem;
  border: 2px solid var(--login-gray-200);
  border-radius: 12px;
  font-size: 1rem;
  color: var(--login-gray-800);
  background-color: var(--login-white);
  transition: var(--login-transition);
  position: relative;
  box-shadow: var(--login-shadow-subtle);
}

form input:focus {
  outline: none;
  border-color: var(--login-primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1), var(--login-shadow-md);
  transform: translateY(-2px);
}

form input:focus + label,
form input:not(:placeholder-shown) + label {
  color: var(--login-primary);
  transform: translateY(-2px);
}

form input:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  background-color: var(--login-gray-50);
}

.login-button {
  padding: 1.25rem 2rem;
  font-size: 1.1rem;
  font-weight: 700;
  background: var(--login-gradient-main);
  color: var(--login-white);
  border: none;
  border-radius: 12px;
  cursor: pointer;
  transition: var(--login-bounce);
  margin-top: 1rem;
  box-shadow: var(--login-shadow-md);
  position: relative;
  overflow: hidden;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.login-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.login-button:hover::before {
  left: 100%;
}

.login-button:hover {
  background: linear-gradient(135deg, var(--login-primary-dark), var(--login-secondary));
  transform: translateY(-3px) scale(1.02);
  box-shadow: var(--login-shadow-lg), var(--login-shadow-glow);
}

.login-button:active {
  transform: translateY(-1px) scale(1.01);
  box-shadow: var(--login-shadow-md);
}

.login-button:disabled {
  background: linear-gradient(135deg, var(--login-gray-400), var(--login-gray-500));
  cursor: not-allowed;
  transform: none;
  box-shadow: var(--login-shadow-subtle);
}

.login-button.loading {
  background: linear-gradient(135deg, var(--login-accent), var(--login-primary));
  animation: loadingPulse 1.5s ease-in-out infinite;
}

@keyframes loadingPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.login-button.loading::after {
  content: '';
  position: absolute;
  width: 20px;
  height: 20px;
  border: 2px solid var(--login-white);
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: buttonSpinner 1s linear infinite;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
}

@keyframes buttonSpinner {
  to { transform: translateY(-50%) rotate(360deg); }
}

.login-button:focus-visible {
  outline: 3px solid rgba(59, 130, 246, 0.5);
  outline-offset: 3px;
}

.signup-text {
  margin-top: 2rem;
  font-size: 1rem;
  color: var(--login-gray-600);
  text-align: center;
}

.signup-link {
  color: var(--login-primary);
  font-weight: 700;
  text-decoration: none;
  transition: var(--login-transition);
  position: relative;
}

.signup-link::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--login-primary);
  transition: width 0.3s ease;
}

.signup-link:hover {
  color: var(--login-primary-dark);
  transform: translateY(-1px);
}

.signup-link:hover::after {
  width: 100%;
}

.error-message {
  background: var(--login-gradient-error);
  border: 2px solid rgba(239, 68, 68, 0.3);
  border-left: 4px solid var(--login-error);
  color: var(--login-gray-800);
  padding: 1rem 1.25rem;
  border-radius: 12px;
  margin-bottom: 1.5rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 1rem;
  animation: errorSlideIn 0.5s ease-out, errorShake 0.6s ease-in-out 0.5s;
  box-shadow: var(--login-shadow-md);
  position: relative;
  overflow: hidden;
}

@keyframes errorSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes errorShake {
  0%, 20%, 40%, 60%, 80% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-3px); }
  15%, 35%, 55%, 75% { transform: translateX(3px); }
}

.error-message::before {
  content: '⚠️';
  font-size: 1.5rem;
  animation: errorPulse 1.5s ease-in-out infinite;
}

@keyframes errorPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.success-message {
  background: var(--login-gradient-success);
  border: 2px solid rgba(16, 185, 129, 0.3);
  border-left: 4px solid var(--login-success);
  color: var(--login-gray-800);
  padding: 1rem 1.25rem;
  border-radius: 12px;
  margin-bottom: 1.5rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 1rem;
  animation: successSlideIn 0.5s ease-out;
  box-shadow: var(--login-shadow-md);
}

@keyframes successSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.success-message::before {
  content: '✅';
  font-size: 1.5rem;
  animation: successBounce 0.6s ease-in-out 0.3s;
}

@keyframes successBounce {
  0%, 20%, 40%, 60%, 80% { transform: translateY(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateY(-5px); }
}

.home-button {
  position: absolute;
  top: 2rem;
  left: 2rem;
  padding: 1rem 1.5rem;
  font-size: 1rem;
  font-weight: 600;
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: var(--login-white);
  border-radius: 12px;
  cursor: pointer;
  transition: var(--login-transition);
  z-index: 20;
  box-shadow: var(--login-shadow-md);
}

.home-button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
  box-shadow: var(--login-shadow-lg);
}

/* Responsive Design */
@media (max-width: 768px) {
  .login-container {
    padding: 1rem;
  }
  
  .steg-slogan {
    font-size: 1.5rem;
    margin-bottom: 2rem;
  }
  
  .login-header h2 {
    font-size: 1.75rem;
  }
  
  .bg-glass {
    padding: 2rem;
    max-width: 95%;
    border-radius: 16px;
  }
  
  .login-logo {
    max-width: 80px;
  }
  
  .input-group label {
    font-size: 0.9rem;
  }
  
  form input {
    padding: 0.875rem 1rem;
    font-size: 0.95rem;
  }
  
  .login-button {
    font-size: 1rem;
    padding: 1rem 1.5rem;
  }
  
  .signup-text {
    font-size: 0.9rem;
  }
  
  .home-button {
    top: 1rem;
    left: 1rem;
    padding: 0.75rem 1rem;
    font-size: 0.9rem;
  }
  
  .login-container .radius-shape-1 {
    height: 120px;
    width: 120px;
    top: -30px;
    left: -60px;
  }
  
  .login-container .radius-shape-2 {
    width: 180px;
    height: 180px;
    bottom: -30px;
    right: -60px;
  }
}

@media (max-width: 480px) {
  .steg-slogan {
    font-size: 1.25rem;
    margin-bottom: 1.5rem;
  }
  
  .bg-glass {
    padding: 1.5rem;
    border-radius: 12px;
  }
  
  .login-header {
    gap: 1rem;
    margin-bottom: 2rem;
  }
  
  .login-header h2 {
    font-size: 1.5rem;
  }
  
  .login-logo {
    max-width: 60px;
  }
  
  form {
    gap: 1.25rem;
  }
  
  form input {
    padding: 0.75rem;
    font-size: 0.9rem;
  }
  
  .login-button {
    padding: 0.875rem 1.25rem;
    font-size: 0.95rem;
  }
  
  .home-button {
    padding: 0.5rem 0.75rem;
    font-size: 0.85rem;
  }
}

/* Dark Theme Support */
@media (prefers-color-scheme: dark) {
  .bg-glass {
    background: rgba(15, 23, 42, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  form label {
    color: var(--login-gray-300);
  }
  
  form input {
    background-color: rgba(30, 41, 59, 0.8);
    border-color: rgba(255, 255, 255, 0.1);
    color: var(--login-gray-100);
  }
  
  form input:focus {
    border-color: var(--login-primary-light);
    background-color: rgba(30, 41, 59, 0.9);
  }
  
  .signup-text {
    color: var(--login-gray-400);
  }
  
  .error-message {
    background: rgba(127, 29, 29, 0.3);
    color: #fca5a5;
  }
  
  .success-message {
    background: rgba(6, 78, 59, 0.3);
    color: #6ee7b7;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .login-button,
  form input {
    border: 2px solid currentColor;
  }
  
  .error-message,
  .success-message {
    border: 3px solid currentColor;
  }
  
  .signup-link {
    text-decoration: underline;
  }
}

/* Print Styles */
@media print {
  .login-container {
    background: white;
    color: black;
  }
  
  .bg-glass {
    background: white;
    box-shadow: none;
    border: 1px solid #ccc;
  }
  
  .login-container::before,
  .login-container .radius-shape-1,
  .login-container .radius-shape-2 {
    display: none;
  }
}
/* Enhanced History.css - Based on Original Structure */
:root {
  /* Color Palette */
  --history-primary: #2563eb;
  --history-primary-dark: #1d4ed8;
  --history-primary-light: #3b82f6;
  --history-secondary: #6366f1;
  --history-accent: #0ea5e9;
  --history-success: #10b981;
  --history-warning: #f59e0b;
  --history-error: #ef4444;
  
  /* Cable Type Colors */
  --cable-calage: #f59e0b;
  --cable-calage-bg: rgba(245, 158, 11, 0.15);
  --cable-calage-text: #92400e;
  --cable-noo: #ef4444;
  --cable-noo-bg: rgba(239, 68, 68, 0.15);
  --cable-noo-text: #991b1b;
  --cable-parfait: #10b981;
  --cable-parfait-bg: rgba(16, 185, 129, 0.15);
  --cable-parfait-text: #047857;
  
  /* Neutral Colors */
  --history-white: #ffffff;
  --history-gray-50: #f8fafc;
  --history-gray-100: #f1f5f9;
  --history-gray-200: #e2e8f0;
  --history-gray-300: #cbd5e1;
  --history-gray-400: #94a3b8;
  --history-gray-500: #64748b;
  --history-gray-600: #475569;
  --history-gray-700: #334155;
  --history-gray-800: #1e293b;
  --history-gray-900: #0f172a;
  
  /* Shadows */
  --history-shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  --history-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --history-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --history-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  
  /* Border Radius */
  --history-radius-sm: 0.25rem;
  --history-radius-md: 0.375rem;
  --history-radius-lg: 0.5rem;
  --history-radius-xl: 0.75rem;
  --history-radius-2xl: 1rem;
  
  /* Transitions */
  --history-transition: all 0.2s ease-in-out;
  --history-transition-slow: all 0.3s ease-in-out;
}

* {
  box-sizing: border-box;
}

body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background-color: var(--history-gray-50);
  color: var(--history-gray-900);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.history-container {
  max-width: 1200px;
  margin: 2rem auto;
  padding: 0 1rem;
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.history-header {
  margin-bottom: 2.5rem;
  text-align: center;
}

.history-header h2 {
  margin-bottom: 1rem;
  color: var(--history-gray-800);
  font-size: 2.5rem;
  font-weight: 800;
  background: linear-gradient(135deg, var(--history-primary) 0%, var(--history-secondary) 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  position: relative;
}

.history-header h2::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 4px;
  background: linear-gradient(135deg, var(--history-primary) 0%, var(--history-secondary) 100%);
  border-radius: var(--history-radius-xl);
  animation: slideIn 0.8s ease-out 0.3s both;
}

@keyframes slideIn {
  from { width: 0; }
  to { width: 60px; }
}

.history-filters {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1.5rem;
  background: linear-gradient(135deg, var(--history-white) 0%, var(--history-gray-50) 100%);
  padding: 2rem;
  border-radius: var(--history-radius-2xl);
  margin-bottom: 2rem;
  box-shadow: var(--history-shadow-lg);
  border: 1px solid var(--history-gray-200);
  position: relative;
  overflow: hidden;
}

.history-filters::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--history-primary), var(--history-accent));
  border-radius: var(--history-radius-2xl) var(--history-radius-2xl) 0 0;
}

.filter-group {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.filter-group label {
  margin-bottom: 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--history-gray-700);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.filter-group label::before {
  content: var(--filter-icon, '🔍');
  font-size: 1rem;
}

.filter-group:nth-child(1) label::before { --filter-icon: '📅'; }
.filter-group:nth-child(2) label::before { --filter-icon: '🔗'; }
.filter-group:nth-child(3) label::before { --filter-icon: '🔍'; }

.filter-group select,
.filter-group input {
  padding: 0.875rem 1rem;
  border: 2px solid var(--history-gray-200);
  border-radius: var(--history-radius-lg);
  font-size: 1rem;
  background-color: var(--history-white);
  color: var(--history-gray-800);
  transition: var(--history-transition);
  min-width: 200px;
  box-shadow: var(--history-shadow-sm);
}

.filter-group select:focus,
.filter-group input:focus {
  outline: none;
  border-color: var(--history-primary);
  box-shadow: 0 0 0 3px rgba(37, 99, 235, 0.1), var(--history-shadow-md);
  transform: translateY(-2px);
}

.filter-group select:hover,
.filter-group input:hover {
  border-color: var(--history-primary-light);
}

.history-error {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(248, 113, 113, 0.05) 100%);
  color: var(--history-gray-800);
  padding: 1.25rem 1.5rem;
  margin-bottom: 2rem;
  border-radius: var(--history-radius-xl);
  border: 2px solid rgba(239, 68, 68, 0.2);
  border-left: 4px solid var(--history-error);
  box-shadow: var(--history-shadow-md);
  display: flex;
  align-items: center;
  gap: 1rem;
  font-weight: 600;
  animation: errorSlideIn 0.5s ease-out, errorShake 0.6s ease-in-out 0.5s;
}

@keyframes errorSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes errorShake {
  0%, 20%, 40%, 60%, 80% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-3px); }
  15%, 35%, 55%, 75% { transform: translateX(3px); }
}

.history-error::before {
  content: '⚠️';
  font-size: 1.5rem;
  animation: errorPulse 1.5s ease-in-out infinite;
}

@keyframes errorPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.history-table-container {
  overflow-x: auto;
  background: var(--history-white);
  border-radius: var(--history-radius-2xl);
  box-shadow: var(--history-shadow-xl);
  border: 1px solid var(--history-gray-200);
  position: relative;
}

.history-table-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--history-primary), var(--history-accent));
  border-radius: var(--history-radius-2xl) var(--history-radius-2xl) 0 0;
}

.history-table {
  width: 100%;
  border-collapse: collapse;
  background-color: transparent;
  position: relative;
}

.history-table th,
.history-table td {
  padding: 1.25rem 1rem;
  text-align: left;
  border-bottom: 1px solid var(--history-gray-200);
  vertical-align: middle;
}

.history-table th {
  background: linear-gradient(135deg, var(--history-gray-50) 0%, var(--history-gray-100) 100%);
  font-weight: 700;
  color: var(--history-gray-700);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  font-size: 0.875rem;
  position: sticky;
  top: 0;
  z-index: 10;
}

.history-table th::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--history-primary), var(--history-accent));
}

.history-table tr {
  transition: var(--history-transition);
  position: relative;
}

.history-table tr:hover {
  background-color: rgba(37, 99, 235, 0.05);
  transform: scale(1.01);
  box-shadow: 0 2px 8px rgba(37, 99, 235, 0.1);
}

.history-table td {
  color: var(--history-gray-700);
  font-weight: 500;
}

.prediction-type {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.625rem 1rem;
  border-radius: var(--history-radius-xl);
  font-size: 0.875rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  transition: var(--history-transition-slow);
  position: relative;
  overflow: hidden;
}

.prediction-type::before {
  content: var(--cable-icon);
  font-size: 1rem;
}

.prediction-type::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
  transition: left 0.5s;
}

.prediction-type:hover::after {
  left: 100%;
}

.prediction-type:hover {
  transform: scale(1.05);
  box-shadow: var(--history-shadow-md);
}

.prediction-type.cable_calage {
  background-color: var(--cable-calage-bg);
  color: var(--cable-calage-text);
  border: 2px solid rgba(245, 158, 11, 0.3);
  --cable-icon: '⚠️';
}

.prediction-type.cable_noo {
  background-color: var(--cable-noo-bg);
  color: var(--cable-noo-text);
  border: 2px solid rgba(239, 68, 68, 0.3);
  --cable-icon: '❌';
}

.prediction-type.cable_parfait {
  background-color: var(--cable-parfait-bg);
  color: var(--cable-parfait-text);
  border: 2px solid rgba(16, 185, 129, 0.3);
  --cable-icon: '✅';
}

.history-empty {
  text-align: center;
  padding: 4rem;
  background: linear-gradient(135deg, var(--history-gray-50) 0%, var(--history-white) 100%);
  border-radius: var(--history-radius-2xl);
  color: var(--history-gray-600);
  border: 2px dashed var(--history-gray-300);
  position: relative;
  overflow: hidden;
}

.history-empty::before {
  content: '📊';
  font-size: 4rem;
  display: block;
  margin-bottom: 1.5rem;
  opacity: 0.5;
  animation: emptyIconFloat 3s ease-in-out infinite;
}

@keyframes emptyIconFloat {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-10px); }
}

.history-empty h3 {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--history-gray-700);
  margin-bottom: 1rem;
}

.history-empty p {
  font-size: 1rem;
  color: var(--history-gray-500);
  margin: 0;
}

.history-loading {
  text-align: center;
  padding: 3rem;
  color: var(--history-gray-600);
  position: relative;
}

.history-loading::before {
  content: '';
  display: inline-block;
  width: 40px;
  height: 40px;
  border: 4px solid var(--history-gray-200);
  border-top: 4px solid var(--history-primary);
  border-radius: 50%;
  animation: loadingSpinner 1s linear infinite;
  margin-bottom: 1.5rem;
}

@keyframes loadingSpinner {
  to { transform: rotate(360deg); }
}

.history-loading p {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0;
}

/* Statistics Cards */
.history-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.stat-card {
  background: linear-gradient(135deg, var(--history-white) 0%, var(--history-gray-50) 100%);
  padding: 1.5rem;
  border-radius: var(--history-radius-xl);
  box-shadow: var(--history-shadow-lg);
  border: 1px solid var(--history-gray-200);
  text-align: center;
  transition: var(--history-transition-slow);
  position: relative;
  overflow: hidden;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: var(--stat-gradient, linear-gradient(90deg, var(--history-primary), var(--history-accent)));
  border-radius: var(--history-radius-xl) var(--history-radius-xl) 0 0;
}

.stat-card:nth-child(1) { --stat-gradient: linear-gradient(90deg, var(--history-success), #34d399); }
.stat-card:nth-child(2) { --stat-gradient: linear-gradient(90deg, var(--history-error), #f87171); }
.stat-card:nth-child(3) { --stat-gradient: linear-gradient(90deg, var(--history-warning), #fbbf24); }

.stat-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--history-shadow-xl);
}

.stat-card h3 {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--history-gray-600);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: 1rem;
}

.stat-card .stat-number {
  font-size: 2rem;
  font-weight: 900;
  color: var(--history-gray-800);
  margin-bottom: 0.5rem;
  line-height: 1;
}

.stat-card .stat-label {
  font-size: 0.875rem;
  color: var(--history-gray-500);
  font-weight: 500;
}

/* Additional Features */
.export-button {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  background: var(--history-white);
  color: var(--history-primary);
  border: 2px solid var(--history-primary);
  border-radius: var(--history-radius-lg);
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--history-transition);
  text-decoration: none;
  margin-bottom: 1.5rem;
}

.export-button:hover {
  background: var(--history-primary);
  color: var(--history-white);
  transform: translateY(-2px);
  box-shadow: var(--history-shadow-md);
}

.export-button::before {
  content: '📤';
  font-size: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .history-container {
    padding: 0 0.75rem;
  }
  
  .history-header h2 {
    font-size: 2rem;
  }
  
  .history-filters {
    grid-template-columns: 1fr;
    gap: 1rem;
    padding: 1.5rem;
  }
  
  .filter-group select,
  .filter-group input {
    min-width: 100%;
  }
  
  .history-table th,
  .history-table td {
    padding: 1rem 0.75rem;
    font-size: 0.875rem;
  }
  
  .prediction-type {
    padding: 0.5rem 0.875rem;
    font-size: 0.8rem;
  }
  
  .history-empty {
    padding: 3rem 1rem;
  }
  
  .history-stats {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 480px) {
  .history-header h2 {
    font-size: 1.75rem;
  }
  
  .history-filters {
    padding: 1rem;
  }
  
  .history-table th,
  .history-table td {
    padding: 0.75rem 0.5rem;
    font-size: 0.8rem;
  }
  
  .prediction-type {
    padding: 0.375rem 0.75rem;
    font-size: 0.75rem;
  }
}

/* Dark Theme Support */
@media (prefers-color-scheme: dark) {
  body {
    background-color: var(--history-gray-900);
    color: var(--history-gray-100);
  }
  
  .history-filters,
  .history-table-container,
  .history-empty,
  .stat-card {
    background: var(--history-gray-800);
    border-color: var(--history-gray-700);
  }
  
  .history-table th {
    background: var(--history-gray-700);
    color: var(--history-gray-200);
  }
  
  .history-table td {
    color: var(--history-gray-300);
  }
  
  .history-table tr:hover {
    background-color: rgba(59, 130, 246, 0.1);
  }
  
  .filter-group select,
  .filter-group input {
    background-color: var(--history-gray-700);
    border-color: var(--history-gray-600);
    color: var(--history-gray-100);
  }
  
  .filter-group label {
    color: var(--history-gray-300);
  }
  
  .history-empty h3 {
    color: var(--history-gray-200);
  }
  
  .history-empty p {
    color: var(--history-gray-400);
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .history-filters,
  .history-table-container,
  .prediction-type,
  .export-button {
    border: 2px solid currentColor;
  }
  
  .history-error {
    border: 3px solid currentColor;
  }
}

/* Print Styles */
@media print {
  body {
    background: white;
    color: black;
  }
  
  .history-filters,
  .history-table-container,
  .history-empty,
  .stat-card {
    background: white;
    box-shadow: none;
    border: 1px solid #ccc;
  }
  
  .export-button {
    display: none;
  }
  
  .prediction-type {
    background: white;
    border: 1px solid currentColor;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus States */
.filter-group select:focus-visible,
.filter-group input:focus-visible,
.export-button:focus-visible {
  outline: 3px solid var(--history-primary);
  outline-offset: 3px;
}

/* Sortable Table Headers */
.history-table th.sortable {
  cursor: pointer;
  user-select: none;
  position: relative;
}

.history-table th.sortable:hover {
  background: rgba(37, 99, 235, 0.1);
}

.history-table th.sortable::after {
  content: '⇅';
  position: absolute;
  right: 0.75rem;
  opacity: 0.5;
  transition: var(--history-transition);
}

.history-table th.sortable.asc::after {
  content: '↑';
  opacity: 1;
  color: var(--history-primary);
}

.history-table th.sortable.desc::after {
  content: '↓';
  opacity: 1;
  color: var(--history-primary);
}
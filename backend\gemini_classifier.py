"""
Module pour la classification de câbles utilisant Gemini Vision API
"""

import os
import base64
import json
import requests
from PIL import Image
import io
import time

class GeminiCableClassifier:
    def __init__(self, api_key=None):
        """
        Initialise le classificateur Gemini

        Args:
            api_key (str): Clé API Google Gemini. Si None, cherche dans les variables d'environnement
        """
        self.api_key = api_key or os.getenv('GEMINI_API_KEY')
        if not self.api_key:
            raise ValueError("Clé API Gemini requise. Définissez GEMINI_API_KEY ou passez api_key")

        self.base_url = "https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent"

        # Prompt spécialisé pour la classification de câbles
        self.classification_prompt = """
Analysez cette image de câble électrique et classifiez-le dans l'une de ces trois catégories :

1. **cable normal** : Câble en bon état, droit, sans défauts visibles
2. **cable redresse** : Câble qui a été redressé ou réparé, peut avoir des marques de manipulation
3. **cable defeaut** : Câble avec des défauts visibles (cassures, déformations, dommages)

Analysez attentivement :
- La forme et la courbure du câble
- La présence de défauts, cassures ou déformations
- L'état général de la surface
- Les signes de réparation ou de manipulation

Répondez UNIQUEMENT avec un JSON dans ce format exact :
{
    "classe": "cable normal" | "cable redresse" | "cable defeaut",
    "confiance": 0.95,
    "explication": "Description détaillée de votre analyse",
    "defauts_detectes": ["liste des défauts observés"],
    "recommandation": "Recommandation d'action"
}

Soyez précis et objectif dans votre analyse.
"""

    def encode_image_to_base64(self, image_path_or_pil):
        """
        Encode une image en base64 pour l'API Gemini avec validation robuste

        Args:
            image_path_or_pil: Chemin vers l'image ou objet PIL Image

        Returns:
            str: Image encodée en base64
        """
        if isinstance(image_path_or_pil, str):
            # Chemin vers fichier - validation robuste
            if not os.path.exists(image_path_or_pil):
                raise ValueError(f"Fichier image non trouvé: {image_path_or_pil}")

            if os.path.getsize(image_path_or_pil) == 0:
                raise ValueError(f"Fichier image vide: {image_path_or_pil}")

            try:
                with open(image_path_or_pil, 'rb') as image_file:
                    image_data = image_file.read()
                    if len(image_data) == 0:
                        raise ValueError("Données d'image vides")

                    # Valider que c'est une image valide
                    try:
                        img = Image.open(io.BytesIO(image_data))
                        img.verify()  # Vérifier l'intégrité
                    except Exception as e:
                        raise ValueError(f"Image corrompue: {e}")

                    encoded = base64.b64encode(image_data).decode('utf-8')
                    if not encoded:
                        raise ValueError("Échec de l'encodage base64")

                    return encoded
            except Exception as e:
                raise ValueError(f"Erreur lors de la lecture du fichier: {e}")

        elif isinstance(image_path_or_pil, Image.Image):
            # Objet PIL Image
            try:
                buffer = io.BytesIO()
                image_path_or_pil.save(buffer, format='JPEG', quality=90)
                buffer_data = buffer.getvalue()

                if len(buffer_data) == 0:
                    raise ValueError("Buffer d'image vide")

                encoded = base64.b64encode(buffer_data).decode('utf-8')
                if not encoded:
                    raise ValueError("Échec de l'encodage base64")

                return encoded
            except Exception as e:
                raise ValueError(f"Erreur lors de l'encodage PIL: {e}")
        else:
            raise ValueError("Format d'image non supporté")

    def classify_cable(self, image_path_or_pil, max_retries=3):
        """
        Classifie un câble en utilisant Gemini Vision

        Args:
            image_path_or_pil: Chemin vers l'image ou objet PIL Image
            max_retries (int): Nombre maximum de tentatives

        Returns:
            dict: Résultats de classification
        """
        try:
            # Encoder l'image
            image_base64 = self.encode_image_to_base64(image_path_or_pil)

            # Préparer la requête
            payload = {
                "contents": [
                    {
                        "parts": [
                            {
                                "text": self.classification_prompt
                            },
                            {
                                "inline_data": {
                                    "mime_type": "image/jpeg",
                                    "data": image_base64
                                }
                            }
                        ]
                    }
                ]
            }

            headers = {
                "Content-Type": "application/json"
            }

            # Faire la requête avec retry
            for attempt in range(max_retries):
                try:
                    response = requests.post(
                        f"{self.base_url}?key={self.api_key}",
                        headers=headers,
                        json=payload,
                        timeout=30
                    )

                    if response.status_code == 200:
                        result = response.json()

                        # Extraire le texte de réponse
                        if 'candidates' in result and len(result['candidates']) > 0:
                            text_response = result['candidates'][0]['content']['parts'][0]['text']

                            # Parser le JSON de réponse
                            try:
                                # Nettoyer la réponse (enlever les markdown si présents)
                                clean_response = text_response.strip()
                                if clean_response.startswith('```json'):
                                    clean_response = clean_response[7:]
                                if clean_response.endswith('```'):
                                    clean_response = clean_response[:-3]

                                classification_result = json.loads(clean_response)

                                # Valider le format de réponse
                                required_fields = ['classe', 'confiance', 'explication']
                                if all(field in classification_result for field in required_fields):
                                    return self._format_result(classification_result)
                                else:
                                    print(f"Champs manquants dans la réponse: {classification_result}")

                            except json.JSONDecodeError as e:
                                print(f"Erreur de parsing JSON: {e}")
                                print(f"Réponse brute: {text_response}")

                        else:
                            print("Pas de candidats dans la réponse")

                    else:
                        print(f"Erreur API: {response.status_code} - {response.text}")

                except requests.exceptions.RequestException as e:
                    print(f"Erreur de requête (tentative {attempt + 1}): {e}")
                    if attempt < max_retries - 1:
                        time.sleep(2 ** attempt)  # Backoff exponentiel

            # Si toutes les tentatives échouent, retourner un résultat par défaut
            return self._get_fallback_result()

        except Exception as e:
            print(f"Erreur lors de la classification Gemini: {e}")
            return self._get_fallback_result()

    def _format_result(self, gemini_result):
        """
        Formate le résultat Gemini pour correspondre au format attendu

        Args:
            gemini_result (dict): Résultat brut de Gemini

        Returns:
            dict: Résultat formaté
        """
        # Mapping des classes pour assurer la cohérence
        class_mapping = {
            'cable normal': 'cable normal',
            'cable redresse': 'cable redresse',
            'cable defeaut': 'cable defeaut',
            'normal': 'cable normal',
            'redresse': 'cable redresse',
            'defeaut': 'cable defeaut'
        }

        predicted_class = gemini_result.get('classe', 'cable normal')
        predicted_class = class_mapping.get(predicted_class, predicted_class)

        confidence = float(gemini_result.get('confiance', 0.8))

        # Créer les probabilités pour toutes les classes
        classes = ['cable defeaut', 'cable normal', 'cable redresse']
        probabilities = [0.1, 0.1, 0.1]  # Probabilités par défaut

        # Assigner la confiance à la classe prédite
        if predicted_class in classes:
            class_idx = classes.index(predicted_class)
            probabilities[class_idx] = confidence

            # Distribuer le reste entre les autres classes
            remaining = 1.0 - confidence
            for i, prob in enumerate(probabilities):
                if i != class_idx:
                    probabilities[i] = remaining / 2

        return {
            'predictions': [
                {
                    'class': classes[i],
                    'probability': probabilities[i]
                }
                for i in range(len(classes))
            ],
            'top_prediction': {
                'class': predicted_class,
                'probability': confidence
            },
            'gemini_analysis': {
                'explication': gemini_result.get('explication', 'Analyse Gemini'),
                'defauts_detectes': gemini_result.get('defauts_detectes', []),
                'recommandation': gemini_result.get('recommandation', 'Aucune recommandation')
            },
            'model_type': 'gemini_vision',
            'raw_confidence': confidence
        }

    def _get_fallback_result(self):
        """
        Retourne un résultat par défaut en cas d'échec

        Returns:
            dict: Résultat par défaut
        """
        return {
            'predictions': [
                {'class': 'cable defeaut', 'probability': 0.33},
                {'class': 'cable normal', 'probability': 0.34},
                {'class': 'cable redresse', 'probability': 0.33}
            ],
            'top_prediction': {
                'class': 'cable normal',
                'probability': 0.34
            },
            'gemini_analysis': {
                'explication': 'Erreur lors de l\'analyse Gemini - résultat par défaut',
                'defauts_detectes': [],
                'recommandation': 'Réessayer l\'analyse'
            },
            'model_type': 'gemini_vision_fallback',
            'raw_confidence': 0.34,
            'error': 'Classification Gemini échouée'
        }

def test_gemini_classifier():
    """
    Fonction de test pour le classificateur Gemini
    """
    try:
        # Initialiser le classificateur
        classifier = GeminiCableClassifier()

        # Tester avec une image d'exemple
        test_image_path = "uploads/test_cable.jpg"  # Remplacer par un chemin d'image réel

        if os.path.exists(test_image_path):
            print("Test du classificateur Gemini...")
            result = classifier.classify_cable(test_image_path)

            print("Résultat de classification:")
            print(json.dumps(result, indent=2, ensure_ascii=False))
        else:
            print(f"Image de test non trouvée: {test_image_path}")

    except Exception as e:
        print(f"Erreur lors du test: {e}")

if __name__ == "__main__":
    test_gemini_classifier()

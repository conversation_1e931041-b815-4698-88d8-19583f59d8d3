"""
Script pour préparer et exécuter l'entraînement sur Google Colab.
Ce script exécute toutes les étapes nécessaires pour préparer les fichiers
et ouvrir le notebook Colab pour l'entraînement.
"""

import os
import sys
import subprocess
import time
import webbrowser

def print_header(message):
    """Affiche un message d'en-tête formaté"""
    print("\n" + "=" * 80)
    print(f"  {message}")
    print("=" * 80 + "\n")

def run_command(command, description):
    """Exécute une commande et affiche sa sortie"""
    print_header(description)
    print(f"Exécution de: {command}")
    
    try:
        process = subprocess.Popen(
            command,
            shell=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True
        )
        
        # Afficher la sortie en temps réel
        for line in process.stdout:
            print(line, end='')
        
        process.wait()
        
        if process.returncode != 0:
            print(f"\nErreur: La commande a échoué avec le code de retour {process.returncode}")
            return False
        
        print("\nCommande exécutée avec succès!")
        return True
    
    except Exception as e:
        print(f"\nException lors de l'exécution de la commande: {str(e)}")
        return False

def prepare_for_colab():
    """Prépare les fichiers pour Google Colab"""
    return run_command("cd backend && python prepare_for_colab.py", "Préparation des fichiers pour Google Colab")

def open_colab_notebook():
    """Ouvre le notebook Colab dans le navigateur"""
    # URL de Colab pour importer un notebook
    colab_url = "https://colab.research.google.com/"
    
    # Ouvrir le navigateur
    print(f"Ouverture de Google Colab dans le navigateur: {colab_url}")
    webbrowser.open(colab_url)
    
    print("\nInstructions pour utiliser Google Colab:")
    print("1. Dans Google Colab, cliquez sur 'Fichier' > 'Importer un notebook'")
    print("2. Sélectionnez l'onglet 'Télécharger'")
    print("3. Téléchargez le fichier 'backend/colab/Cable_Classification_Training.ipynb'")
    print("4. Téléchargez le fichier ZIP du dataset depuis le dossier 'backend/colab/'")
    print("5. Téléchargez-le dans votre Google Drive")
    print("6. Suivez les instructions dans le notebook Colab")
    
    return True

def show_instructions():
    """Affiche les instructions pour l'entraînement sur Google Colab"""
    print_header("INSTRUCTIONS POUR L'ENTRAÎNEMENT SUR GOOGLE COLAB")
    
    print("Fichiers préparés:")
    print("1. Dataset ZIP: backend/colab/cable_dataset.zip")
    print("2. Notebook Colab: backend/colab/Cable_Classification_Training.ipynb")
    print("3. Script d'entraînement: backend/colab/colab_train_model.py")
    
    print("\nÉtapes à suivre:")
    print("1. Téléchargez le fichier ZIP du dataset sur votre ordinateur")
    print("2. Téléchargez-le dans votre Google Drive dans un dossier nommé 'cable_dataset'")
    print("3. Ouvrez le notebook Colab dans Google Colab")
    print("4. Exécutez les cellules du notebook dans l'ordre")
    print("5. Téléchargez le modèle entraîné à la fin du processus")
    
    print("\nPour plus d'informations, consultez le guide:")
    print("backend/COLAB_GUIDE.md")

def main():
    """Fonction principale"""
    print_header("PRÉPARATION ET EXÉCUTION DE L'ENTRAÎNEMENT SUR GOOGLE COLAB")
    
    # Préparer les fichiers pour Google Colab
    if not prepare_for_colab():
        print("Erreur: Impossible de préparer les fichiers pour Google Colab.")
        return
    
    # Ouvrir le notebook Colab
    if not open_colab_notebook():
        print("Erreur: Impossible d'ouvrir le notebook Colab.")
        return
    
    # Afficher les instructions
    show_instructions()
    
    print_header("PRÉPARATION TERMINÉE")
    print("Suivez les instructions ci-dessus pour entraîner le modèle sur Google Colab.")

if __name__ == "__main__":
    main()

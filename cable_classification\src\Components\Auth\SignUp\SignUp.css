/* Enhanced SignUp.css - 10x Better */
:root {
  /* SignUp Color Palette */
  --signup-primary: #3b82f6;
  --signup-primary-dark: #1d4ed8;
  --signup-primary-light: #60a5fa;
  --signup-secondary: #8b5cf6;
  --signup-accent: #06b6d4;
  --signup-success: #10b981;
  --signup-warning: #f59e0b;
  --signup-error: #ef4444;
  
  /* Gradients */
  --signup-gradient-main: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --signup-gradient-glass: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
  --signup-gradient-error: linear-gradient(135deg, #fee2e2 0%, #fecaca 100%);
  --signup-gradient-success: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
  
  /* Neutral Colors */
  --signup-white: #ffffff;
  --signup-gray-50: #f9fafb;
  --signup-gray-100: #f3f4f6;
  --signup-gray-200: #e5e7eb;
  --signup-gray-300: #d1d5db;
  --signup-gray-400: #9ca3af;
  --signup-gray-500: #6b7280;
  --signup-gray-600: #4b5563;
  --signup-gray-700: #374151;
  --signup-gray-800: #1f2937;
  --signup-gray-900: #111827;
  
  /* Shadows */
  --signup-shadow-subtle: 0 1px 2px rgba(0, 0, 0, 0.05);
  --signup-shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  --signup-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --signup-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --signup-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --signup-shadow-glow: 0 0 40px rgba(59, 130, 246, 0.3);
  
  /* Transitions */
  --signup-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --signup-bounce: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.signup-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: var(--signup-gradient-main);
  background-size: 400% 400%;
  animation: gradientShift 8s ease infinite;
  padding: 2rem;
  position: relative;
  overflow: hidden;
}

@keyframes gradientShift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.signup-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 30%),
    radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.1) 0%, transparent 30%),
    radial-gradient(circle at 50% 50%, rgba(139, 92, 246, 0.2) 0%, transparent 40%);
  animation: particlesFloat 15s ease-in-out infinite;
  z-index: 1;
}

@keyframes particlesFloat {
  0%, 100% { transform: translateY(0) rotate(0deg); }
  33% { transform: translateY(-15px) rotate(1deg); }
  66% { transform: translateY(10px) rotate(-1deg); }
}

/* Enhanced animated shapes */
.signup-container .radius-shape-1,
.signup-container .radius-shape-2 {
  position: absolute;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.4), rgba(139, 92, 246, 0.3));
  opacity: 0.7;
  border-radius: 50%;
  animation: floatingShapes 8s ease-in-out infinite;
  z-index: 1;
  filter: blur(1px);
}

.signup-container .radius-shape-1 {
  height: 250px;
  width: 250px;
  top: -60px;
  left: -120px;
  animation-delay: 0s;
  background: linear-gradient(135deg, rgba(59, 130, 246, 0.4), rgba(16, 185, 129, 0.3));
}

.signup-container .radius-shape-2 {
  width: 350px;
  height: 350px;
  bottom: -60px;
  right: -120px;
  border-radius: 38% 62% 63% 37% / 70% 33% 67% 30%;
  animation-delay: -3s;
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.4), rgba(6, 182, 212, 0.3));
}

@keyframes floatingShapes {
  0%, 100% { transform: translate(0, 0) rotate(0deg) scale(1); }
  25% { transform: translate(30px, -30px) rotate(8deg) scale(1.1); }
  50% { transform: translate(-20px, 20px) rotate(-5deg) scale(0.9); }
  75% { transform: translate(25px, 10px) rotate(3deg) scale(1.05); }
}

.bg-glass {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(25px) saturate(180%);
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 3rem;
  border-radius: 24px;
  box-shadow: var(--signup-shadow-xl);
  text-align: center;
  max-width: 500px;
  width: 100%;
  position: relative;
  z-index: 10;
  animation: cardSlideUp 0.8s ease-out;
  overflow: hidden;
}

@keyframes cardSlideUp {
  from {
    opacity: 0;
    transform: translateY(50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.bg-glass::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--signup-gradient-main);
  border-radius: 24px 24px 0 0;
  animation: topBorderGlow 2s ease-in-out infinite;
}

@keyframes topBorderGlow {
  0%, 100% { opacity: 0.8; }
  50% { opacity: 1; }
}

.bg-glass::after {
  content: '';
  position: absolute;
  top: 20px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, var(--signup-primary), var(--signup-secondary));
  border-radius: 50%;
  opacity: 0.1;
  animation: logoFloat 3s ease-in-out infinite;
}

@keyframes logoFloat {
  0%, 100% { transform: translateX(-50%) translateY(0) scale(1); }
  50% { transform: translateX(-50%) translateY(-10px) scale(1.1); }
}

h2 {
  font-size: 2.5rem;
  font-weight: 900;
  background: var(--signup-gradient-main);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 2rem;
  position: relative;
  animation: titleSlide 0.8s ease-out 0.3s both;
}

@keyframes titleSlide {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

h2::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 3px;
  background: var(--signup-gradient-main);
  border-radius: 2px;
  animation: underlineGrow 1s ease-out 0.8s both;
}

@keyframes underlineGrow {
  from { width: 0; }
  to { width: 80px; }
}

form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  animation: formSlide 0.8s ease-out 0.5s both;
}

@keyframes formSlide {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.input-group {
  display: flex;
  flex-direction: column;
  text-align: left;
  position: relative;
}

form label {
  font-size: 1rem;
  font-weight: 600;
  color: var(--signup-gray-700);
  margin-bottom: 0.5rem;
  transition: var(--signup-transition);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

form label::before {
  content: var(--label-icon, '👤');
  font-size: 1.2rem;
  transition: var(--signup-transition);
}

.input-group:nth-child(1) label::before { --label-icon: '👤'; }
.input-group:nth-child(2) label::before { --label-icon: '📧'; }
.input-group:nth-child(3) label::before { --label-icon: '🔒'; }
.input-group:nth-child(4) label::before { --label-icon: '✅'; }

form input {
  padding: 1rem 1.25rem;
  border: 2px solid var(--signup-gray-200);
  border-radius: 12px;
  font-size: 1rem;
  color: var(--signup-gray-800);
  background-color: var(--signup-white);
  transition: var(--signup-transition);
  position: relative;
  box-shadow: var(--signup-shadow-subtle);
  font-family: inherit;
}

form input:focus {
  outline: none;
  border-color: var(--signup-primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1), var(--signup-shadow-md);
  transform: translateY(-2px);
  background-color: rgba(59, 130, 246, 0.02);
}

form input:focus + label,
form input:not(:placeholder-shown) + label {
  color: var(--signup-primary);
  transform: translateY(-2px);
}

form input:focus + label::before {
  transform: scale(1.2);
}

form input:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  background-color: var(--signup-gray-50);
}

form input:valid {
  border-color: var(--signup-success);
}

form input:invalid:not(:focus):not(:placeholder-shown) {
  border-color: var(--signup-error);
}

.signup-button {
  padding: 1.25rem 2rem;
  font-size: 1.1rem;
  font-weight: 700;
  background: var(--signup-gradient-main);
  color: var(--signup-white);
  border: none;
  border-radius: 12px;
  cursor: pointer;
  transition: var(--signup-bounce);
  margin-top: 1rem;
  box-shadow: var(--signup-shadow-md);
  position: relative;
  overflow: hidden;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.signup-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.signup-button:hover::before {
  left: 100%;
}

.signup-button:hover {
  background: linear-gradient(135deg, var(--signup-primary-dark), var(--signup-secondary));
  transform: translateY(-3px) scale(1.02);
  box-shadow: var(--signup-shadow-lg), var(--signup-shadow-glow);
}

.signup-button:active {
  transform: translateY(-1px) scale(1.01);
  box-shadow: var(--signup-shadow-md);
}

.signup-button:disabled {
  background: linear-gradient(135deg, var(--signup-gray-400), var(--signup-gray-500));
  cursor: not-allowed;
  transform: none;
  box-shadow: var(--signup-shadow-subtle);
}

.signup-button.loading {
  background: linear-gradient(135deg, var(--signup-accent), var(--signup-primary));
  animation: loadingPulse 1.5s ease-in-out infinite;
}

@keyframes loadingPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.signup-button.loading::after {
  content: '';
  position: absolute;
  width: 20px;
  height: 20px;
  border: 2px solid var(--signup-white);
  border-top: 2px solid transparent;
  border-radius: 50%;
  animation: buttonSpinner 1s linear infinite;
  right: 20px;
  top: 50%;
  transform: translateY(-50%);
}

@keyframes buttonSpinner {
  to { transform: translateY(-50%) rotate(360deg); }
}

.signup-button:focus-visible {
  outline: 3px solid rgba(59, 130, 246, 0.5);
  outline-offset: 3px;
}

.login-link-text {
  margin-top: 2rem;
  font-size: 1rem;
  color: var(--signup-gray-600);
  text-align: center;
  animation: linkSlide 0.8s ease-out 0.7s both;
}

@keyframes linkSlide {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.login-link {
  color: var(--signup-primary);
  font-weight: 700;
  text-decoration: none;
  transition: var(--signup-transition);
  position: relative;
  display: inline-block;
}

.login-link::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--signup-primary);
  transition: width 0.3s ease;
}

.login-link:hover {
  color: var(--signup-primary-dark);
  transform: translateY(-1px);
}

.login-link:hover::after {
  width: 100%;
}

/* Error and Success Messages */
.error-message {
  background: var(--signup-gradient-error);
  border: 2px solid rgba(239, 68, 68, 0.3);
  border-left: 4px solid var(--signup-error);
  color: var(--signup-gray-800);
  padding: 1rem 1.25rem;
  border-radius: 12px;
  margin-bottom: 1.5rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 1rem;
  animation: errorSlideIn 0.5s ease-out, errorShake 0.6s ease-in-out 0.5s;
  box-shadow: var(--signup-shadow-md);
  position: relative;
  overflow: hidden;
}

@keyframes errorSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes errorShake {
  0%, 20%, 40%, 60%, 80% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-3px); }
  15%, 35%, 55%, 75% { transform: translateX(3px); }
}

.error-message::before {
  content: '⚠️';
  font-size: 1.5rem;
  animation: errorPulse 1.5s ease-in-out infinite;
}

@keyframes errorPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.success-message {
  background: var(--signup-gradient-success);
  border: 2px solid rgba(16, 185, 129, 0.3);
  border-left: 4px solid var(--signup-success);
  color: var(--signup-gray-800);
  padding: 1rem 1.25rem;
  border-radius: 12px;
  margin-bottom: 1.5rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 1rem;
  animation: successSlideIn 0.5s ease-out;
  box-shadow: var(--signup-shadow-md);
}

@keyframes successSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.success-message::before {
  content: '✅';
  font-size: 1.5rem;
  animation: successBounce 0.6s ease-in-out 0.3s;
}

@keyframes successBounce {
  0%, 20%, 40%, 60%, 80% { transform: translateY(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateY(-5px); }
}

/* Password Strength Indicator */
.password-strength {
  margin-top: 0.5rem;
  font-size: 0.875rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.password-strength-bar {
  flex: 1;
  height: 4px;
  background-color: var(--signup-gray-200);
  border-radius: 2px;
  overflow: hidden;
}

.password-strength-fill {
  height: 100%;
  transition: width 0.3s ease, background-color 0.3s ease;
  border-radius: 2px;
}

.password-strength.weak .password-strength-fill {
  width: 33%;
  background-color: var(--signup-error);
}

.password-strength.medium .password-strength-fill {
  width: 66%;
  background-color: var(--signup-warning);
}

.password-strength.strong .password-strength-fill {
  width: 100%;
  background-color: var(--signup-success);
}

/* Form Validation */
.input-group.error input {
  border-color: var(--signup-error);
  background-color: rgba(239, 68, 68, 0.05);
}

.input-group.success input {
  border-color: var(--signup-success);
  background-color: rgba(16, 185, 129, 0.05);
}

.field-error {
  font-size: 0.875rem;
  color: var(--signup-error);
  margin-top: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  animation: fieldErrorSlide 0.3s ease-out;
}

@keyframes fieldErrorSlide {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.field-error::before {
  content: '❌';
  font-size: 1rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .signup-container {
    padding: 1rem;
  }
  
  h2 {
    font-size: 2rem;
    margin-bottom: 1.5rem;
  }
  
  .bg-glass {
    padding: 2rem;
    max-width: 95%;
    border-radius: 16px;
  }
  
  form {
    gap: 1.25rem;
  }
  
  form label {
    font-size: 0.9rem;
  }
  
  form input {
    padding: 0.875rem 1rem;
    font-size: 0.95rem;
  }
  
  .signup-button {
    font-size: 1rem;
    padding: 1rem 1.5rem;
  }
  
  .login-link-text {
    font-size: 0.9rem;
  }
  
  .signup-container .radius-shape-1 {
    height: 150px;
    width: 150px;
    top: -40px;
    left: -80px;
  }
  
  .signup-container .radius-shape-2 {
    width: 200px;
    height: 200px;
    bottom: -40px;
    right: -80px;
  }
}

@media (max-width: 480px) {
  h2 {
    font-size: 1.75rem;
    margin-bottom: 1.25rem;
  }
  
  .bg-glass {
    padding: 1.5rem;
    border-radius: 12px;
  }
  
  form {
    gap: 1rem;
  }
  
  form label {
    font-size: 0.85rem;
  }
  
  form input {
    padding: 0.75rem;
    font-size: 0.9rem;
  }
  
  .signup-button {
    padding: 0.875rem 1.25rem;
    font-size: 0.95rem;
  }
  
  .login-link-text {
    font-size: 0.85rem;
  }
  
  .signup-container .radius-shape-1 {
    height: 100px;
    width: 100px;
    top: -30px;
    left: -50px;
  }
  
  .signup-container .radius-shape-2 {
    width: 130px;
    height: 130px;
    bottom: -30px;
    right: -50px;
  }
}

/* Dark Theme Support */
@media (prefers-color-scheme: dark) {
  .bg-glass {
    background: rgba(15, 23, 42, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  form label {
    color: var(--signup-gray-300);
  }
  
  form input {
    background-color: rgba(30, 41, 59, 0.8);
    border-color: rgba(255, 255, 255, 0.1);
    color: var(--signup-gray-100);
  }
  
  form input:focus {
    border-color: var(--signup-primary-light);
    background-color: rgba(30, 41, 59, 0.9);
  }
  
  .login-link-text {
    color: var(--signup-gray-400);
  }
  
  .error-message {
    background: rgba(127, 29, 29, 0.3);
    color: #fca5a5;
  }
  
  .success-message {
    background: rgba(6, 78, 59, 0.3);
    color: #6ee7b7;
  }
  
  .field-error {
    color: #fca5a5;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .signup-button,
  form input {
    border: 2px solid currentColor;
  }
  
  .error-message,
  .success-message {
    border: 3px solid currentColor;
  }
  
  .login-link {
    text-decoration: underline;
  }
}

/* Print Styles */
@media print {
  .signup-container {
    background: white;
    color: black;
  }
  
  .bg-glass {
    background: white;
    box-shadow: none;
    border: 1px solid #ccc;
  }
  
  .signup-container::before,
  .signup-container .radius-shape-1,
  .signup-container .radius-shape-2 {
    display: none;
  }
}

/* Accessibility Enhancements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  
  .signup-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }
}

/* Focus trap for form navigation */
.bg-glass:focus-within {
  outline: 3px solid rgba(59, 130, 246, 0.3);
  outline-offset: 4px;
}

/* Enhanced hover states for better interactivity */
.input-group:hover label::before {
  transform: scale(1.1);
  filter: brightness(1.2);
}

.input-group:hover input {
  border-color: var(--signup-primary-light);
  box-shadow: var(--signup-shadow-sm);
}
.model-performance {
  padding: 2rem;
  max-width: 1200px;
  margin: 0 auto;
}

.model-performance h2 {
  font-size: 2rem;
  margin-bottom: 1.5rem;
  color: #2c3e50;
}

.model-selector {
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  gap: 1rem;
}

.model-selector label {
  font-weight: 600;
  color: #2c3e50;
}

.model-select {
  padding: 0.5rem 1rem;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 1rem;
  min-width: 300px;
}

.metrics-overview {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.metrics-overview h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: #2c3e50;
}

.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.metric-card {
  background-color: white;
  border-radius: 8px;
  padding: 1.5rem;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease;
}

.metric-card:hover {
  transform: translateY(-5px);
}

.metric-value {
  font-size: 2.5rem;
  font-weight: bold;
  color: #3498db;
  margin-bottom: 0.5rem;
}

.metric-label {
  font-size: 1rem;
  color: #7f8c8d;
}

.confusion-matrix {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.confusion-matrix h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: #2c3e50;
}

.matrix-container {
  position: relative;
  padding: 2rem;
}

.matrix-labels {
  position: relative;
}

.matrix-axis-label {
  position: absolute;
  font-weight: bold;
  color: #2c3e50;
}

.matrix-axis-label.vertical {
  transform: rotate(-90deg);
  left: -40px;
  top: 50%;
}

.matrix-table {
  width: 100%;
  border-collapse: collapse;
  margin: 0 auto;
}

.matrix-table th,
.matrix-table td {
  padding: 1rem;
  text-align: center;
  border: 1px solid #ddd;
}

.matrix-table th {
  background-color: #f2f2f2;
  font-weight: 600;
}

.matrix-table td.correct {
  background-color: #d4edda;
  color: #155724;
  font-weight: bold;
}

.matrix-table td.incorrect {
  background-color: #f8d7da;
  color: #721c24;
}

.training-history {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 1.5rem;
  margin-bottom: 2rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.training-history h3 {
  font-size: 1.5rem;
  margin-bottom: 1rem;
  color: #2c3e50;
}

.history-chart {
  margin-top: 1.5rem;
}

.chart-placeholder {
  position: relative;
  height: 300px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
  padding: 1rem;
}

.chart-lines {
  position: relative;
  height: 100%;
  width: 100%;
}

.chart-line {
  position: absolute;
  bottom: 0;
  width: 2px;
}

.chart-line.accuracy {
  background-color: #3498db;
}

.chart-line.validation {
  background-color: #e74c3c;
}

.chart-point {
  position: absolute;
  top: 0;
  left: -4px;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background-color: inherit;
}

.chart-legend {
  display: flex;
  justify-content: center;
  gap: 2rem;
  margin-top: 1rem;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.legend-color {
  width: 20px;
  height: 10px;
  border-radius: 2px;
}

.legend-color.accuracy {
  background-color: #3498db;
}

.legend-color.validation {
  background-color: #e74c3c;
}

.model-actions {
  display: flex;
  gap: 1rem;
  margin-top: 2rem;
}

.action-button {
  background-color: #3498db;
  color: white;
  border: none;
  border-radius: 4px;
  padding: 0.75rem 1.5rem;
  font-size: 1rem;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.action-button:hover {
  background-color: #2980b9;
}

@media (max-width: 768px) {
  .model-performance {
    padding: 1rem;
  }
  
  .model-selector {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .model-select {
    width: 100%;
  }
  
  .metrics-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .model-actions {
    flex-direction: column;
  }
  
  .action-button {
    width: 100%;
  }
}

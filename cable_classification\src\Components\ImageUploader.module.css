/* Enhanced ImageUploader.module.css */
:root {
  /* Color Palette */
  --primary: #3b82f6;
  --primary-dark: #1d4ed8;
  --primary-light: #60a5fa;
  --secondary: #8b5cf6;
  --accent: #06b6d4;
  --success: #22c55e; /* Green for cable normal */
  --warning: #f97316; /* Orange for cable redresse */
  --error: #dc2626; /* Red for cable defeaut */
  --info: #3b82f6;

  /* Gradients */
  --gradient-primary: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  --gradient-success: linear-gradient(135deg, #22c55e 0%, #4ade80 100%); /* Green gradient for cable normal */
  --gradient-error: linear-gradient(135deg, #dc2626 0%, #ef4444 100%); /* Red gradient for cable defeaut */
  --gradient-warning: linear-gradient(135deg, #f97316 0%, #fb923c 100%); /* Orange gradient for cable redresse */
  --gradient-glass: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.8) 100%);

  /* Neutral Colors */
  --white: #ffffff;
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  /* Shadows */
  --shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --shadow-glow: 0 0 20px rgba(59, 130, 246, 0.2);

  /* Border Radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;
  --radius-full: 9999px;
}

/* Base Container */
.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f0f9ff 0%, #e0f2fe 50%, #f0f9ff 100%);
  padding: 2rem;
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
  position: relative;
  overflow-x: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2rem;
}

.container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 20%, rgba(59, 130, 246, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(139, 92, 246, 0.05) 0%, transparent 50%);
  animation: backgroundShift 15s ease-in-out infinite;
  z-index: 0;
}

@keyframes backgroundShift {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.container > * {
  position: relative;
  z-index: 1;
}

/* Upload Section */
.uploadSection {
  width: 100%;
  max-width: 700px;
  background: var(--gradient-glass);
  backdrop-filter: blur(20px) saturate(150%);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: var(--radius-2xl);
  padding: 2.5rem;
  box-shadow: var(--shadow-xl);
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
}

.uploadSection::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--gradient-primary);
  border-radius: var(--radius-2xl) var(--radius-2xl) 0 0;
}

/* Upload Buttons Container */
.uploadButtons {
  display: flex;
  gap: 1rem;
  width: 100%;
  justify-content: center;
  flex-wrap: wrap;
}

/* Button Styles */
.uploadButton,
.cameraButton,
.predictButton {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: var(--radius-lg);
  font-weight: 600;
  cursor: pointer;
  border: none;
  outline: none;
  transition: all 0.2s ease;
  min-width: 180px;
  box-shadow: var(--shadow-md);
  position: relative;
  overflow: hidden;
}

.uploadButton,
.cameraButton {
  background: var(--gradient-primary);
  color: var(--white);
}

.uploadButton:hover,
.cameraButton:hover {
  box-shadow: var(--shadow-lg), var(--shadow-glow);
}

.uploadButton:active,
.cameraButton:active {
  transform: translateY(1px);
  box-shadow: var(--shadow-sm);
}

.cameraButton.active {
  background: var(--primary-dark);
}

.actionButtons {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
  width: 100%;
  justify-content: center;
}

.predictButton {
  background: var(--gradient-success);
  color: var(--white);
  font-size: 1.125rem;
  padding: 1rem 2rem;
  flex: 1;
  max-width: 200px;
}

.predictButton:hover {
  box-shadow: var(--shadow-lg), 0 0 15px rgba(16, 185, 129, 0.4);
}

.detectButton {
  background: var(--gradient-primary);
  color: var(--white);
  font-size: 1.125rem;
  padding: 1rem 2rem;
  flex: 1;
  max-width: 250px;
}

.detectButton:hover {
  box-shadow: var(--shadow-lg), 0 0 15px rgba(59, 130, 246, 0.4);
}

.buttonIcon {
  margin-right: 0.5rem;
}

.uploadButton:disabled,
.cameraButton:disabled,
.predictButton:disabled {
  background: var(--gray-400);
  cursor: not-allowed;
  opacity: 0.7;
  box-shadow: none;
}

/* Image Container */
.imageContainer {
  width: 100%;
  max-width: 350px;
  height: 350px;
  background: var(--white);
  border: 2px dashed var(--gray-300);
  border-radius: var(--radius-lg);
  overflow: hidden;
  position: relative;
  box-shadow: var(--shadow-md);
  transition: all 0.3s ease;
}

.imageContainer.hasImage {
  border-style: solid;
  border-color: var(--primary);
}

.imageContainer:hover {
  box-shadow: var(--shadow-lg);
}

.uploadedImage {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: var(--radius-md);
}

.clearImageButton {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  background: rgba(255, 255, 255, 0.8);
  color: var(--gray-700);
  border: none;
  border-radius: var(--radius-full);
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(4px);
  z-index: 5;
}

.clearImageButton:hover {
  background: var(--white);
  color: var(--error);
}

.imageInfo {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: rgba(0, 0, 0, 0.6);
  color: var(--white);
  padding: 0.5rem;
  font-size: 0.75rem;
  display: flex;
  justify-content: space-between;
  backdrop-filter: blur(4px);
}

/* Camera Container */
.cameraContainer {
  width: 100%;
  max-width: 500px;
  background: var(--gray-900);
  border-radius: var(--radius-lg);
  overflow: hidden;
  position: relative;
  box-shadow: var(--shadow-lg);
}

.videoPreview {
  width: 100%;
  display: block;
}

/* Real-time status indicator */
.realTimeStatus {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: rgba(220, 38, 38, 0.8);
  color: white;
  padding: 0.5rem 1rem;
  border-radius: var(--radius-full);
  font-weight: 600;
  font-size: 0.875rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  z-index: 10;
  backdrop-filter: blur(4px);
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
  animation: fadeIn 0.3s ease;
}

/* Camera Controls */
.cameraControls {
  position: absolute;
  bottom: 1rem;
  left: 0;
  right: 0;
  display: flex;
  justify-content: center;
  gap: 1rem;
  z-index: 5;
}

.captureButton {
  background: var(--white);
  color: var(--gray-900);
  border: none;
  border-radius: var(--radius-full);
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  cursor: pointer;
  box-shadow: var(--shadow-md);
}

.captureButton:hover {
  background: var(--gray-50);
  box-shadow: var(--shadow-lg);
}

.captureButton:disabled {
  background: var(--gray-300);
  cursor: not-allowed;
}

.retryButton {
  position: absolute;
  bottom: 1rem;
  left: 50%;
  transform: translateX(-50%);
  background: var(--gradient-warning) !important;
  color: var(--white) !important;
  animation: pulseWarning 2s infinite;
}

@keyframes pulseWarning {
  0% { transform: translateX(-50%) scale(1); }
  50% { transform: translateX(-50%) scale(1.05); }
  100% { transform: translateX(-50%) scale(1); }
}

/* Real-time mode button */
.realTimeButton {
  background: var(--primary-dark);
  color: var(--white);
  border: none;
  border-radius: var(--radius-full);
  padding: 0.75rem 1.5rem;
  font-weight: 600;
  cursor: pointer;
  box-shadow: var(--shadow-md);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  transition: all 0.3s ease;
}

.realTimeButton:hover {
  background: var(--primary);
  box-shadow: var(--shadow-lg);
}

.realTimeButton.active {
  background: var(--error);
  animation: pulse 2s infinite;
}

.recordingIndicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: var(--white);
  display: inline-block;
  margin-right: 0.5rem;
  animation: blink 1s infinite;
}

@keyframes blink {
  0% { opacity: 1; }
  50% { opacity: 0.3; }
  100% { opacity: 1; }
}

@keyframes pulse {
  0% { box-shadow: 0 0 0 0 rgba(220, 38, 38, 0.4); }
  70% { box-shadow: 0 0 0 10px rgba(220, 38, 38, 0); }
  100% { box-shadow: 0 0 0 0 rgba(220, 38, 38, 0); }
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(-10px); }
  to { opacity: 1; transform: translateY(0); }
}

.retryButton:hover {
  background: var(--gradient-error) !important;
  transform: translateX(-50%) translateY(-3px) !important;
  box-shadow: var(--shadow-lg), 0 0 20px rgba(239, 68, 68, 0.4) !important;
}

.loadingOverlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
  backdrop-filter: blur(2px);
}

.loadingSpinner {
  width: 3rem;
  height: 3rem;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top-color: var(--white);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* Model Selector */
.modelSelector {
  width: 100%;
  background: var(--white);
  border-radius: var(--radius-lg);
  padding: 1.25rem;
  box-shadow: var(--shadow-sm);
}

.modelSelector label {
  display: block;
  margin-bottom: 0.5rem;
  font-weight: 600;
  color: var(--gray-700);
}

.modelSelect {
  width: 100%;
  padding: 0.75rem 1rem;
  border-radius: var(--radius-md);
  border: 1px solid var(--gray-300);
  font-size: 1rem;
  color: var(--gray-800);
  background-color: var(--white);
  outline: none;
  transition: all 0.2s ease;
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%236b7280' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 1rem center;
  background-size: 1em;
  padding-right: 2.5rem;
}

.modelSelect:focus {
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

.modelSelect:disabled {
  background-color: var(--gray-100);
  cursor: not-allowed;
}

.modelFixed {
  width: 100%;
  padding: 0.75rem 1rem;
  border-radius: var(--radius-md);
  border: 1px solid var(--primary);
  font-size: 1rem;
  font-weight: 600;
  color: var(--primary-dark);
  background-color: var(--gray-50);
  text-align: center;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Prediction Button Loading State */
.predictButton.loading {
  position: relative;
  padding-right: 3rem;
}

.predictButton.loading:after {
  content: '';
  position: absolute;
  right: 1rem;
  width: 1.25rem;
  height: 1.25rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  border-top-color: var(--white);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Error Message */
.error {
  width: 100%;
  max-width: 700px;
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem 1.5rem;
  background: var(--gradient-error);
  color: var(--white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  font-weight: 500;
}

.error p {
  margin: 0;
  flex: 1;
}

/* Result Container */
.resultContainer {
  width: 100%;
  max-width: 700px;
  background: var(--white);
  border-radius: var(--radius-xl);
  overflow: hidden;
  box-shadow: var(--shadow-xl);
  transition: all 0.3s ease;
}

.resultContainer.success {
  border-top: 5px solid var(--success);
}

.resultContainer.warning {
  border-top: 5px solid var(--warning);
}

.resultContainer.error {
  border-top: 5px solid var(--error);
}

/* Real-time result styling */
.resultContainer.realTimeResult {
  animation: realTimePulse 2s infinite;
}

@keyframes realTimePulse {
  0% { box-shadow: var(--shadow-xl); }
  50% { box-shadow: 0 10px 30px rgba(220, 38, 38, 0.2); }
  100% { box-shadow: var(--shadow-xl); }
}

.liveIndicator {
  display: inline-block;
  background-color: var(--error);
  color: white;
  font-size: 0.75rem;
  font-weight: 700;
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-full);
  margin-left: 0.75rem;
  animation: blink 1s infinite;
  vertical-align: middle;
}

.detectionBadge {
  display: inline-block;
  background-color: var(--primary);
  color: white;
  font-size: 0.75rem;
  font-weight: 700;
  padding: 0.25rem 0.5rem;
  border-radius: var(--radius-full);
  margin-left: 0.75rem;
  vertical-align: middle;
}

/* Annotated image styles */
.annotatedImageContainer {
  width: 100%;
  padding: 1rem;
  background-color: var(--gray-100);
  border-radius: var(--radius-lg);
  margin-bottom: 1rem;
  text-align: center;
}

.annotatedImage {
  max-width: 100%;
  max-height: 400px;
  border-radius: var(--radius-md);
  box-shadow: var(--shadow-md);
}

.resultTitle {
  margin: 0;
  padding: 1.5rem;
  font-size: 1.5rem;
  font-weight: 700;
  text-align: center;
  border-bottom: 1px solid var(--gray-200);
  color: var(--gray-800);
}

.resultContent {
  padding: 1.5rem;
}

.resultMain {
  margin-bottom: 2rem;
}

.resultTopClass {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1rem;
}

.resultIcon {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 1rem;
}

.resultIcon svg {
  width: 2.5rem;
  height: 2.5rem;
}

/* Color the icons based on result status */
.success .resultIcon svg {
  stroke: var(--success);
  fill: rgba(34, 197, 94, 0.1);
}

.warning .resultIcon svg {
  stroke: var(--warning);
  fill: rgba(249, 115, 22, 0.1);
}

.error .resultIcon svg {
  stroke: var(--error);
  fill: rgba(220, 38, 38, 0.1);
}

.resultTopClass h3 {
  font-size: 1.75rem;
  font-weight: 800;
  margin: 0;
  color: var(--gray-800);
  flex: 1;
}

.confidenceBadge {
  padding: 0.5rem 1rem;
  border-radius: var(--radius-full);
  font-weight: 700;
  font-size: 1.125rem;
}

.confidenceBadge.success {
  background: var(--success);
  color: var(--white);
}

.confidenceBadge.warning {
  background: var(--warning);
  color: var(--white);
}

.confidenceBadge.error {
  background: var(--error);
  color: var(--white);
}

.resultMeta {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
  margin-top: 1rem;
  background: var(--gray-50);
  padding: 1rem;
  border-radius: var(--radius-md);
  font-size: 0.875rem;
  color: var(--gray-600);
}

.resultMeta p {
  margin: 0;
}

.timestamp {
  text-align: right;
  font-size: 0.75rem;
  color: var(--gray-500);
  margin-top: 0.5rem;
}

.predictionsDetail {
  background: var(--gray-50);
  border-radius: var(--radius-lg);
  padding: 1.5rem;
}

.predictionsDetail h4 {
  margin: 0 0 1rem 0;
  font-size: 1rem;
  color: var(--gray-700);
}

.predictionItem {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 0.75rem;
}

.predictionName {
  width: 140px;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--gray-800);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.predictionBarContainer {
  flex: 1;
  height: 0.75rem;
  background: var(--gray-200);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.predictionBar {
  height: 100%;
  background: var(--primary);
  border-radius: var(--radius-full);
}

.predictionBar.topPrediction {
  background: var(--success);
}

/* Different colors for different cable types */
.success .predictionBar.topPrediction {
  background: var(--success); /* Green for cable normal */
}

.warning .predictionBar.topPrediction {
  background: var(--warning); /* Orange for cable redresse */
}

.error .predictionBar.topPrediction {
  background: var(--error); /* Red for cable defeaut */
}

.predictionValue {
  width: 50px;
  text-align: right;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--gray-700);
}

/* Responsive styles */
@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }

  .uploadSection {
    padding: 1.5rem;
  }

  .imageContainer {
    max-width: 300px;
    height: 300px;
  }

  .uploadButtons {
    flex-direction: column;
    align-items: center;
  }

  .uploadButton,
  .cameraButton,
  .predictButton {
    width: 100%;
  }

  .resultTopClass {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }

  .confidenceBadge {
    align-self: flex-start;
  }
}

@media (max-width: 480px) {
  .imageContainer {
    height: 250px;
  }

  .predictionItem {
    flex-wrap: wrap;
  }

  .predictionName {
    width: 100%;
  }

  .predictionValue {
    width: auto;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .container {
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%);
  }

  .uploadSection {
    background: rgba(15, 23, 42, 0.8);
    border-color: rgba(255, 255, 255, 0.1);
  }

  .imageContainer {
    background: var(--gray-800);
    border-color: var(--gray-700);
  }

  .modelSelector,
  .resultContainer {
    background: var(--gray-800);
    color: var(--gray-200);
  }

  .modelSelector label,
  .resultTitle,
  .resultTopClass h3 {
    color: var(--gray-200);
  }

  .modelSelect {
    background-color: var(--gray-700);
    border-color: var(--gray-600);
    color: var(--gray-200);
  }

  .predictionsDetail {
    background: var(--gray-700);
  }

  .predictionsDetail h4 {
    color: var(--gray-300);
  }

  .predictionName {
    color: var(--gray-300);
  }

  .predictionBarContainer {
    background: var(--gray-600);
  }

  .resultMeta {
    background: var(--gray-700);
    color: var(--gray-300);
  }

  .clearImageButton {
    background: rgba(31, 41, 55, 0.8);
    color: var(--gray-300);
  }

  .clearImageButton:hover {
    background: var(--gray-800);
    color: var(--error);
  }
}
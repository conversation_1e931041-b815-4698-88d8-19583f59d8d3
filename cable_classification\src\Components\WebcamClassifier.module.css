/* Container principal */
.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
  background: #f8f9fa;
  min-height: 100vh;
}

/* Header */
.header {
  text-align: center;
  margin-bottom: 30px;
  padding: 30px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.header h1 {
  font-size: 2.5rem;
  margin-bottom: 10px;
  font-weight: 700;
}

.header p {
  font-size: 1.1rem;
  opacity: 0.9;
  margin: 0;
}

/* Main content */
.mainContent {
  background: white;
  border-radius: 20px;
  padding: 30px;
  box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

/* Webcam container */
.webcamContainer {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 30px;
  margin-bottom: 30px;
}

/* Video section */
.videoSection {
  text-align: center;
}

.videoWrapper {
  position: relative;
  border-radius: 15px;
  overflow: hidden;
  box-shadow: 0 10px 30px rgba(0,0,0,0.2);
  margin-bottom: 20px;
  background: #000;
}

.webcam {
  width: 100%;
  height: auto;
  display: block;
  max-height: 400px;
  object-fit: cover;
}

.videoOverlay {
  position: absolute;
  top: 10px;
  left: 10px;
  right: 10px;
  background: rgba(0,0,0,0.7);
  color: white;
  padding: 10px;
  border-radius: 10px;
  font-size: 0.9rem;
  display: flex;
  justify-content: space-between;
}

/* Controls */
.controls {
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
}

.btn {
  padding: 12px 24px;
  border: none;
  border-radius: 25px;
  font-size: 1rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.btnPrimary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.btnSuccess {
  background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
  color: white;
}

.btnWarning {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Results section */
.resultsSection {
  display: grid;
  grid-template-columns: 1fr;
  gap: 20px;
}

.resultCard {
  background: #f8f9fa;
  border-radius: 15px;
  padding: 25px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
  height: fit-content;
}

.resultCard h3 {
  margin-bottom: 20px;
  color: #333;
  font-size: 1.3rem;
  display: flex;
  align-items: center;
  gap: 10px;
}

/* Status indicators */
.statusIndicator {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #ddd;
}

.statusIndicator.active {
  background: #28a745;
  animation: pulse 2s infinite;
}

.statusIndicator.processing {
  background: #ffc107;
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

/* Live indicator */
.liveIndicator {
  background: #dc3545;
  color: white;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.7rem;
  font-weight: bold;
  margin-left: 10px;
  animation: pulse 1s infinite;
}

/* Predictions */
.prediction {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 15px;
  padding: 15px;
  border-radius: 10px;
  font-weight: 600;
}

.prediction.cablenormal {
  background: #d4edda;
  color: #155724;
  border-left: 4px solid #28a745;
}

.prediction.cableredresse {
  background: #fff3cd;
  color: #856404;
  border-left: 4px solid #ffc107;
}

.prediction.cabledefeaut {
  background: #f8d7da;
  color: #721c24;
  border-left: 4px solid #dc3545;
}

.predictionIcon {
  display: flex;
  align-items: center;
}

.confidence {
  font-size: 1.2rem;
  margin-top: 5px;
}

/* Probabilities */
.probabilities {
  margin-top: 15px;
}

.probabilities h4 {
  margin-bottom: 10px;
  color: #333;
  font-size: 1rem;
}

.probabilityBar {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 0.9rem;
}

.probabilityLabel {
  width: 120px;
  font-weight: 500;
  font-size: 0.8rem;
}

.probabilityValue {
  flex: 1;
  height: 8px;
  background: #e9ecef;
  border-radius: 4px;
  margin: 0 10px;
  overflow: hidden;
  position: relative;
}

.probabilityFill {
  height: 100%;
  border-radius: 4px;
  transition: width 0.3s ease;
}

.probabilityText {
  width: 50px;
  text-align: right;
  font-weight: 600;
  font-size: 0.8rem;
}

/* Gemini analysis */
.geminiAnalysis {
  margin-top: 20px;
  padding: 15px;
  background: #e3f2fd;
  border-radius: 10px;
  border-left: 4px solid #2196f3;
}

.geminiAnalysis h4 {
  color: #1976d2;
  margin-bottom: 10px;
  font-size: 1rem;
}

.geminiAnalysis p {
  margin-bottom: 8px;
  line-height: 1.5;
  font-size: 0.9rem;
}

/* Performance stats */
.performanceStats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
  margin-top: 20px;
}

.statCard {
  background: white;
  padding: 20px;
  border-radius: 15px;
  text-align: center;
  box-shadow: 0 5px 15px rgba(0,0,0,0.1);
  border: 2px solid #f8f9fa;
  transition: all 0.3s ease;
}

.statCard:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0,0,0,0.15);
}

.statValue {
  font-size: 2rem;
  font-weight: bold;
  color: #667eea;
  margin-bottom: 5px;
}

.statLabel {
  font-size: 0.9rem;
  color: #666;
  font-weight: 500;
}

/* Error message */
.errorMessage {
  background: #f8d7da;
  color: #721c24;
  padding: 15px;
  border-radius: 10px;
  margin: 15px 0;
  border-left: 4px solid #dc3545;
  font-weight: 500;
}

/* Responsive design */
@media (max-width: 768px) {
  .webcamContainer,
  .resultsSection {
    grid-template-columns: 1fr;
  }

  .header h1 {
    font-size: 2rem;
  }

  .controls {
    flex-direction: column;
    align-items: center;
  }

  .btn {
    width: 200px;
    justify-content: center;
  }

  .performanceStats {
    grid-template-columns: repeat(2, 1fr);
  }

  .container {
    padding: 10px;
  }

  .mainContent {
    padding: 20px;
  }
}

@media (max-width: 480px) {
  .performanceStats {
    grid-template-columns: 1fr;
  }

  .probabilityLabel {
    width: 80px;
    font-size: 0.7rem;
  }

  .probabilityText {
    width: 40px;
    font-size: 0.7rem;
  }
}

import React, { useState, useEffect } from 'react';
import './DatasetViewer.css';

const DatasetViewer = () => {
  const [datasetStats, setDatasetStats] = useState({
    totalImages: 1318,
    trainImages: 919,
    validationImages: 134,
    testImages: 265,
    categories: [
      { id: 1, name: 'Cable Damage Type 1', count: 523 },
      { id: 2, name: 'Cable Damage Type 2', count: 795 }
    ]
  });
  
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [sampleImages, setSampleImages] = useState([
    { id: 1, url: '/sample_images/cable_damage_1.jpg', category: 1 },
    { id: 2, url: '/sample_images/cable_damage_2.jpg', category: 1 },
    { id: 3, url: '/sample_images/cable_damage_3.jpg', category: 2 },
    { id: 4, url: '/sample_images/cable_damage_4.jpg', category: 2 },
    { id: 5, url: '/sample_images/cable_damage_5.jpg', category: 1 },
    { id: 6, url: '/sample_images/cable_damage_6.jpg', category: 2 }
  ]);
  
  const [displayedImages, setDisplayedImages] = useState([]);
  
  useEffect(() => {
    // Filter images based on selected category
    if (selectedCategory) {
      setDisplayedImages(sampleImages.filter(img => img.category === selectedCategory));
    } else {
      setDisplayedImages(sampleImages);
    }
  }, [selectedCategory, sampleImages]);
  
  const handleCategorySelect = (categoryId) => {
    setSelectedCategory(categoryId === selectedCategory ? null : categoryId);
  };
  
  return (
    <div className="dataset-viewer">
      <h2>Dataset Explorer</h2>
      <p className="dataset-description">
        This dataset contains cable damage images for training and evaluation of the classification model.
        The dataset is sourced from <a href="https://huggingface.co/datasets/Francesco/cable-damage" target="_blank" rel="noopener noreferrer">
          Francesco/cable-damage
        </a> on Hugging Face.
      </p>
      
      <div className="dataset-stats">
        <h3>Dataset Statistics</h3>
        <div className="stats-grid">
          <div className="stat-card">
            <div className="stat-value">{datasetStats.totalImages}</div>
            <div className="stat-label">Total Images</div>
          </div>
          <div className="stat-card">
            <div className="stat-value">{datasetStats.trainImages}</div>
            <div className="stat-label">Training Images</div>
          </div>
          <div className="stat-card">
            <div className="stat-value">{datasetStats.validationImages}</div>
            <div className="stat-label">Validation Images</div>
          </div>
          <div className="stat-card">
            <div className="stat-value">{datasetStats.testImages}</div>
            <div className="stat-label">Test Images</div>
          </div>
        </div>
      </div>
      
      <div className="dataset-categories">
        <h3>Categories</h3>
        <div className="category-filters">
          {datasetStats.categories.map(category => (
            <button 
              key={category.id}
              className={`category-filter ${selectedCategory === category.id ? 'active' : ''}`}
              onClick={() => handleCategorySelect(category.id)}
            >
              {category.name} ({category.count})
            </button>
          ))}
        </div>
      </div>
      
      <div className="sample-images">
        <h3>Sample Images</h3>
        <div className="image-grid">
          {displayedImages.map(image => (
            <div key={image.id} className="image-card">
              <img 
                src={image.url} 
                alt={`Cable damage sample ${image.id}`} 
                className="sample-image"
              />
              <div className="image-info">
                <span className="image-category">
                  Category: {datasetStats.categories.find(c => c.id === image.category).name}
                </span>
              </div>
            </div>
          ))}
        </div>
      </div>
      
      <div className="model-training">
        <h3>Model Training</h3>
        <p>
          This dataset is used to train the EfficientNet model for cable damage classification.
          The model is trained on the training set and evaluated on the validation set.
          The test set is used for final evaluation of the model performance.
        </p>
        <div className="training-actions">
          <button className="action-button">Train New Model</button>
          <button className="action-button">Evaluate Model</button>
          <button className="action-button">Export Model</button>
        </div>
      </div>
    </div>
  );
};

export default DatasetViewer;

import React, { useState, useRef, useEffect, useCallback } from 'react';
import styles from './GeminiWebcam.module.css';

// Icons
const CameraIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
    <path d="M23 19a2 2 0 0 1-2 2H3a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h4l2-3h6l2 3h4a2 2 0 0 1 2 2z"></path>
    <circle cx="12" cy="13" r="4"></circle>
  </svg>
);

const ZapIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
    <polygon points="13 2 3 14 12 14 11 22 21 10 12 10 13 2"></polygon>
  </svg>
);

const CheckCircleIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
    <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
    <polyline points="22 4 12 14.01 9 11.01"></polyline>
  </svg>
);

const AlertCircleIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
    <circle cx="12" cy="12" r="10"></circle>
    <line x1="12" y1="8" x2="12" y2="12"></line>
    <line x1="12" y1="16" x2="12.01" y2="16"></line>
  </svg>
);

const XCircleIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
    <circle cx="12" cy="12" r="10"></circle>
    <line x1="15" y1="9" x2="9" y2="15"></line>
    <line x1="9" y1="9" x2="15" y2="15"></line>
  </svg>
);

const PREDICTION_CLASSES = {
  'cable defeaut': 'Câble Défectueux',
  'cable normal': 'Câble Normal',
  'cable redresse': 'Câble Redressé',
};

function GeminiWebcam() {
  // State management
  const [webcamState, setWebcamState] = useState({
    isActive: false,
    hasPermission: true,
    error: null
  });

  const [geminiResults, setGeminiResults] = useState(null);
  const [geminiProcessing, setGeminiProcessing] = useState(false);
  const [isRealTimeMode, setIsRealTimeMode] = useState(false);

  const [stats, setStats] = useState({
    geminiTime: 0,
    totalClassifications: 0,
    fps: 0
  });

  // Refs
  const videoRef = useRef(null);
  const canvasRef = useRef(null);
  const streamRef = useRef(null);
  const classificationIntervalRef = useRef(null);
  const fpsIntervalRef = useRef(null);
  const lastFrameTimeRef = useRef(0);

  // Start webcam
  const startWebcam = useCallback(async () => {
    try {
      setWebcamState(prev => ({ ...prev, error: null }));

      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          width: { ideal: 1280 },
          height: { ideal: 720 },
          facingMode: 'environment'
        }
      });

      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        streamRef.current = stream;

        setWebcamState(prev => ({
          ...prev,
          isActive: true,
          hasPermission: true
        }));

        startFpsCounter();
      }
    } catch (error) {
      console.error('Webcam error:', error);
      let errorMessage = "Impossible d'accéder à la webcam: " + error.message;

      if (error.name === 'NotAllowedError') {
        errorMessage = "Accès à la webcam refusé. Veuillez autoriser l'accès.";
        setWebcamState(prev => ({ ...prev, hasPermission: false }));
      }

      setWebcamState(prev => ({ ...prev, error: errorMessage }));
    }
  }, []);

  // Stop webcam
  const stopWebcam = useCallback(() => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
    }

    if (classificationIntervalRef.current) {
      clearInterval(classificationIntervalRef.current);
    }

    if (fpsIntervalRef.current) {
      clearInterval(fpsIntervalRef.current);
    }

    setWebcamState(prev => ({ ...prev, isActive: false }));
    setGeminiResults(null);
    setIsRealTimeMode(false);
  }, []);

  // Stop realtime mode only
  const stopRealtimeMode = useCallback(() => {
    if (classificationIntervalRef.current) {
      clearInterval(classificationIntervalRef.current);
    }
    setIsRealTimeMode(false);
  }, []);

  // Start auto classification with Gemini
  const startAutoClassification = useCallback(() => {
    classificationIntervalRef.current = setInterval(() => {
      if (webcamState.isActive && !geminiProcessing && isRealTimeMode) {
        analyzeWithGemini();
      }
    }, 15000); // Every 15 seconds for Gemini (quota-friendly)
  }, [webcamState.isActive, geminiProcessing, isRealTimeMode]);

  // Start FPS counter
  const startFpsCounter = useCallback(() => {
    fpsIntervalRef.current = setInterval(() => {
      const now = performance.now();
      if (lastFrameTimeRef.current) {
        const fps = 1000 / (now - lastFrameTimeRef.current);
        setStats(prev => ({ ...prev, fps: fps.toFixed(1) }));
      }
      lastFrameTimeRef.current = now;
    }, 100);
  }, []);

  // Toggle real-time mode
  const toggleRealTimeMode = useCallback(() => {
    setIsRealTimeMode(prev => {
      const newMode = !prev;

      if (newMode && webcamState.isActive) {
        // Start auto classification when enabling real-time mode
        startAutoClassification();
      } else if (!newMode && classificationIntervalRef.current) {
        // Stop auto classification when disabling real-time mode
        clearInterval(classificationIntervalRef.current);
      }

      return newMode;
    });
  }, [webcamState.isActive, startAutoClassification]);

  // Analyze with Gemini
  const analyzeWithGemini = useCallback(async () => {
    if (!videoRef.current || geminiProcessing) return;

    try {
      setGeminiProcessing(true);

      // Clear any previous errors
      setWebcamState(prev => ({ ...prev, error: null }));

      // Capture frame
      const canvas = canvasRef.current;
      const video = videoRef.current;

      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;

      const ctx = canvas.getContext('2d');
      ctx.drawImage(video, 0, 0);

      // Convert canvas to blob with error handling
      const blob = await new Promise((resolve, reject) => {
        canvas.toBlob((blob) => {
          if (blob) {
            resolve(blob);
          } else {
            reject(new Error('Failed to create image blob'));
          }
        }, 'image/jpeg', 0.9);
      });

      // Validate blob
      if (!blob || blob.size === 0) {
        throw new Error('Image capture failed - empty blob');
      }

      // Create FormData with proper error handling
      const formData = new FormData();
      try {
        formData.append('image', blob, 'webcam.jpg');
      } catch (formError) {
        throw new Error('Failed to create form data: ' + formError.message);
      }

      const startTime = performance.now();
      const response = await fetch('http://localhost:5000/api/webcam/classify_gemini', {
        method: 'POST',
        body: formData
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      const endTime = performance.now();

      if (data.error) {
        throw new Error(data.error);
      }

      if (data.gemini_results) {
        setGeminiResults(data.gemini_results);
        setStats(prev => ({
          ...prev,
          geminiTime: ((endTime - startTime) / 1000).toFixed(1),
          totalClassifications: prev.totalClassifications + 1
        }));
      } else {
        throw new Error('No results received from Gemini');
      }
    } catch (error) {
      console.error('Gemini classification error:', error);
      setWebcamState(prev => ({
        ...prev,
        error: 'Erreur lors de l\'analyse Gemini: ' + error.message
      }));
    } finally {
      setGeminiProcessing(false);
    }
  }, [geminiProcessing]);

  // Capture and analyze with Gemini
  const captureAndAnalyze = useCallback(async () => {
    if (!videoRef.current || geminiProcessing) return;

    try {
      setGeminiProcessing(true);

      // Clear any previous errors
      setWebcamState(prev => ({ ...prev, error: null }));

      // Capture frame
      const canvas = canvasRef.current;
      const video = videoRef.current;

      canvas.width = video.videoWidth;
      canvas.height = video.videoHeight;

      const ctx = canvas.getContext('2d');
      ctx.drawImage(video, 0, 0);

      // Convert canvas to blob with error handling
      const blob = await new Promise((resolve, reject) => {
        canvas.toBlob((blob) => {
          if (blob) {
            resolve(blob);
          } else {
            reject(new Error('Failed to create image blob'));
          }
        }, 'image/jpeg', 0.9);
      });

      // Validate blob
      if (!blob || blob.size === 0) {
        throw new Error('Image capture failed - empty blob');
      }

      // Download image locally
      const link = document.createElement('a');
      link.download = `capture_${new Date().toISOString().replace(/[:.]/g, '-')}.jpg`;
      link.href = canvas.toDataURL('image/jpeg');
      link.click();

      // Create FormData for Gemini analysis
      const formData = new FormData();
      try {
        formData.append('image', blob, 'webcam.jpg');
      } catch (formError) {
        throw new Error('Failed to create form data: ' + formError.message);
      }

      const startTime = performance.now();
      const response = await fetch('http://localhost:5000/api/webcam/classify_gemini', {
        method: 'POST',
        body: formData
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const data = await response.json();
      const endTime = performance.now();

      if (data.error) {
        throw new Error(data.error);
      }

      if (data.gemini_results) {
        setGeminiResults(data.gemini_results);
        setStats(prev => ({
          ...prev,
          geminiTime: ((endTime - startTime) / 1000).toFixed(1),
          totalClassifications: prev.totalClassifications + 1
        }));
      } else {
        throw new Error('No results received from Gemini');
      }
    } catch (error) {
      console.error('Capture and analyze error:', error);
      setWebcamState(prev => ({
        ...prev,
        error: 'Erreur lors de la capture et analyse: ' + error.message
      }));
    } finally {
      setGeminiProcessing(false);
    }
  }, [geminiProcessing]);

  // Get color for class
  const getColorForClass = (className) => {
    switch (className) {
      case 'cable normal': return '#28a745';
      case 'cable redresse': return '#ffc107';
      case 'cable defeaut': return '#dc3545';
      default: return '#6c757d';
    }
  };

  // Get icon for class
  const getIconForClass = (className) => {
    switch (className) {
      case 'cable normal': return <CheckCircleIcon />;
      case 'cable redresse': return <AlertCircleIcon />;
      case 'cable defeaut': return <XCircleIcon />;
      default: return <AlertCircleIcon />;
    }
  };

  // Auto-start real-time mode when webcam starts
  useEffect(() => {
    if (webcamState.isActive && isRealTimeMode) {
      startAutoClassification();
    }
  }, [webcamState.isActive, isRealTimeMode, startAutoClassification]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      stopWebcam();
    };
  }, [stopWebcam]);

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h1>🤖 Classification Model Vision</h1>
        <p>Intelligence Artificielle Avancée pour Classification de Câbles en Temps Réel</p>
      </div>

      <div className={styles.mainContent}>
        <div className={styles.webcamContainer}>
          <div className={styles.videoSection}>
            <div className={styles.videoWrapper}>
              <video
                ref={videoRef}
                autoPlay
                muted
                playsInline
                className={styles.webcam}
              />
              <div className={styles.videoOverlay}>
                <div>FPS: {stats.fps || '--'}</div>
                <div>
                  {webcamState.isActive ?
                    (isRealTimeMode ? '🔴 LIVE Model' : '⚪ Webcam Active') :
                    'Webcam Inactive'
                  }
                </div>
              </div>
            </div>

            <div className={styles.controls}>
              {/* Bouton 1: Démarrer/Arrêter */}
              <button
                onClick={webcamState.isActive ? stopWebcam : startWebcam}
                className={`${styles.btn} ${styles.btnPrimary} ${webcamState.isActive ? styles.btnDanger : ''}`}
              >
                <CameraIcon />
                {webcamState.isActive ? '🛑 ARRÊTER' : '📹 DÉMARRER'}
              </button>

              {/* Bouton 2: Analyser en temps réel */}
              <button
                onClick={toggleRealTimeMode}
                disabled={!webcamState.isActive}
                className={`${styles.btn} ${isRealTimeMode ? styles.btnDanger : styles.btnSuccess}`}
              >
                <ZapIcon />
                {isRealTimeMode ? '🛑 ARRÊTER TEMPS RÉEL' : '⚡ ACTIVER TEMPS RÉEL'}
              </button>

              {/* Bouton 3: Capturer et analyser */}
              <button
                onClick={captureAndAnalyze}
                disabled={!webcamState.isActive || geminiProcessing}
                className={`${styles.btn} ${styles.btnWarning}`}
              >
                📸 {geminiProcessing ? 'CAPTURE & ANALYSE...' : 'CAPTURER & ANALYSER'}
              </button>
            </div>
          </div>

          <div className={styles.resultsSection}>
            <div className={styles.resultCard}>
              <h3>
                <span className={`${styles.statusIndicator} ${geminiProcessing ? styles.processing : (geminiResults ? styles.active : '')}`}></span>
                🤖 IA model
                {isRealTimeMode && <span className={styles.liveIndicator}>🔴 LIVE</span>}
              </h3>
              <div className={styles.resultContent}>
                {geminiResults ? (
                  <>
                    <div className={`${styles.prediction} ${styles[geminiResults.top_prediction.class.replace(' ', '')]}`}>
                      <div className={styles.predictionIcon}>
                        {getIconForClass(geminiResults.top_prediction.class)}
                      </div>
                      <div>
                        <strong>{PREDICTION_CLASSES[geminiResults.top_prediction.class]}</strong>
                        <div className={styles.confidence}>
                          {(geminiResults.top_prediction.probability * 100).toFixed(1)}%
                        </div>
                      </div>
                    </div>

                    <div className={styles.probabilities}>
                      <h4>Probabilités:</h4>
                      {geminiResults.predictions.map((pred, index) => (
                        <div key={index} className={styles.probabilityBar}>
                          <div className={styles.probabilityLabel}>
                            {PREDICTION_CLASSES[pred.class]}
                          </div>
                          <div className={styles.probabilityValue}>
                            <div
                              className={styles.probabilityFill}
                              style={{
                                width: `${pred.probability * 100}%`,
                                background: getColorForClass(pred.class)
                              }}
                            ></div>
                          </div>
                          <div className={styles.probabilityText}>
                            {(pred.probability * 100).toFixed(1)}%
                          </div>
                        </div>
                      ))}
                    </div>

                    {geminiResults.gemini_analysis && (
                      <div className={styles.geminiAnalysis}>
                        <h4>🤖 Analyse Détaillée</h4>
                        {geminiResults.gemini_analysis.explication && (
                          <p><strong>Explication:</strong> {geminiResults.gemini_analysis.explication}</p>
                        )}
                        {geminiResults.gemini_analysis.defauts_detectes && geminiResults.gemini_analysis.defauts_detectes.length > 0 && (
                          <p><strong>Défauts:</strong> {geminiResults.gemini_analysis.defauts_detectes.join(', ')}</p>
                        )}
                        {geminiResults.gemini_analysis.recommandation && (
                          <p><strong>Recommandation:</strong> {geminiResults.gemini_analysis.recommandation}</p>
                        )}
                      </div>
                    )}
                  </>
                ) : (
                  <div className={styles.waitingState}>
                    <p>🎯 Activez le mode temps réel ou cliquez sur "Analyser Maintenant"</p>
                    <p>🤖 Model Vision analysera intelligemment vos câbles</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>

        <div className={styles.performanceStats}>
          <div className={styles.statCard}>
            <div className={styles.statValue}>{stats.geminiTime || '--'}</div>
            <div className={styles.statLabel}>Temps Model (s)</div>
          </div>
          <div className={styles.statCard}>
            <div className={styles.statValue}>{stats.totalClassifications}</div>
            <div className={styles.statLabel}>Classifications</div>
          </div>
          <div className={styles.statCard}>
            <div className={styles.statValue}>{isRealTimeMode ? '🔴 ON' : '⚪ OFF'}</div>
            <div className={styles.statLabel}>Mode Temps Réel</div>
          </div>
          <div className={styles.statCard}>
            <div className={styles.statValue}>{stats.fps || '--'}</div>
            <div className={styles.statLabel}>FPS Webcam</div>
          </div>
        </div>
      </div>

      <canvas ref={canvasRef} style={{ display: 'none' }} />

      {webcamState.error && (
        <div className={styles.errorMessage}>
          <div className={styles.errorHeader}>
            ⚠️ Erreur détectée
            <button
              onClick={() => setWebcamState(prev => ({ ...prev, error: null }))}
              className={styles.errorClose}
            >
              ✕
            </button>
          </div>
          <div className={styles.errorContent}>
            {webcamState.error}
          </div>
          <div className={styles.errorActions}>
            <button
              onClick={() => {
                setWebcamState(prev => ({ ...prev, error: null }));
                if (!webcamState.isActive) {
                  startWebcam();
                }
              }}
              className={`${styles.btn} ${styles.btnPrimary}`}
            >
              🔄 Réessayer
            </button>
          </div>
        </div>
      )}
    </div>
  );
}

export default GeminiWebcam;

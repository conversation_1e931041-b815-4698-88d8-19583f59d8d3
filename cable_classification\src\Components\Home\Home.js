import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { auth, db } from '../../firebase';
import './Home.css';

const Home = () => {
  const [user, setUser] = useState(null);
  const [userData, setUserData] = useState({
    name: '',
    role: ''
  });
  const [recentPredictions, setRecentPredictions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');

  useEffect(() => {
    const fetchUserData = async () => {
      try {
        const currentUser = auth.currentUser;
        if (currentUser) {
          setUser(currentUser);

          // Get user data from Firestore
          const userDoc = await db.collection('users').doc(currentUser.uid).get();
          if (userDoc.exists) {
            const data = userDoc.data();
            setUserData({
              name: data.name || '',
              role: data.role || 'employee'
            });
          }

          // Get recent predictions
          const predictionsCollection = db.collection('types_cables');
          const querySnapshot = await predictionsCollection.get();

          const predictions = [];
          querySnapshot.forEach(doc => {
            const data = doc.data();
            const date = doc.id;

            if (data.lastUpdated) {
              predictions.push({
                date,
                timestamp: data.lastUpdated.toDate(),
                cable_calage: data.cable_calage || 0,
                cable_noo: data.cable_noo || 0,
                cable_parfait: data.cable_parfait || 0
              });
            }
          });

          // Sort by timestamp (most recent first) and take the 5 most recent
          predictions.sort((a, b) => b.timestamp - a.timestamp);
          setRecentPredictions(predictions.slice(0, 5));
        }
      } catch (err) {
        console.error("Error fetching data:", err);
        setError("Erreur lors du chargement des données");
      } finally {
        setLoading(false);
      }
    };

    fetchUserData();
  }, []);

  const formatDate = (date) => {
    return new Date(date).toLocaleDateString('fr-FR');
  };

  const getLatestPrediction = (prediction) => {
    const { cable_calage, cable_noo, cable_parfait } = prediction;

    if (cable_calage > cable_noo && cable_calage > cable_parfait) {
      return 'Câble Calage';
    } else if (cable_noo > cable_calage && cable_noo > cable_parfait) {
      return 'Câble Noo';
    } else if (cable_parfait > cable_calage && cable_parfait > cable_noo) {
      return 'Câble Parfait';
    } else {
      return 'Multiple ou aucune détection claire';
    }
  };

  if (loading) {
    return <div className="home-loading">Chargement...</div>;
  }

  return (
    <div className="home-container">
      <div className="home-layout">
        {/* Sidebar with app description */}
        <div className="home-sidebar">
          <div className="app-logo">
            <img src="/cable.png" alt="Logo" className="logo-image" />
            <h2>Cable Classification</h2>
          </div>

          <div className="app-description">
            <h3>À propos de l'application</h3>
            <p>
              Notre application de classification de câbles utilise l'intelligence artificielle pour identifier et
              catégoriser différents types de câbles avec une grande précision.
            </p>

            <h4>Caractéristiques principales:</h4>
            <ul>
              <li>
                <span className="feature-icon">🔍</span>
                <span className="feature-text">Classification précise des câbles en trois catégories: Câble Calage, Câble Noo et Câble Parfait</span>
              </li>
              <li>
                <span className="feature-icon">🤖</span>
                <span className="feature-text">Utilisation du modèle EfficientNet pour une analyse d'image de pointe</span>
              </li>
              <li>
                <span className="feature-icon">📊</span>
                <span className="feature-text">Suivi des statistiques et historique des classifications</span>
              </li>
              <li>
                <span className="feature-icon">📱</span>
                <span className="feature-text">Interface intuitive et responsive</span>
              </li>
            </ul>

            <div className="app-version">
              <p>Version: 1.0.0</p>
              <p>© 2023 Cable Classification</p>
            </div>
          </div>
        </div>

        {/* Main content */}
        <div className="home-main-content">
          <div className="home-header">
            <h1>Bienvenue, {userData.name || 'Utilisateur'}</h1>
            <p className="home-subtitle">Système de Classification de Câbles</p>
          </div>

          {error && <div className="home-error">{error}</div>}

          <div className="home-cards">
            <div className="home-card">
              <div className="card-icon classification-icon">
                <i className="fas fa-camera"></i>
              </div>
              <h2>Classification</h2>
              <p>Analysez et classifiez des images de câbles en utilisant notre modèle EfficientNet.</p>
              <Link to="/Classification" className="card-button">
                Classifier une image
              </Link>
            </div>

            <div className="home-card">
              <div className="card-icon history-icon">
                <i className="fas fa-history"></i>
              </div>
              <h2>Historique</h2>
              <p>Consultez l'historique des classifications précédentes et les statistiques.</p>
              <Link to="/History" className="card-button">
                Voir l'historique
              </Link>
            </div>

            {userData.role === 'admin' && (
              <div className="home-card">
                <div className="card-icon admin-icon">
                  <i className="fas fa-chart-bar"></i>
                </div>
                <h2>Administration</h2>
                <p>Accédez au tableau de bord administrateur pour voir les statistiques détaillées.</p>
                <Link to="/Admin" className="card-button">
                  Tableau de bord
                </Link>
              </div>
            )}
          </div>

          <div className="home-recent">
            <h2>Activité récente</h2>
            {recentPredictions.length > 0 ? (
              <div className="recent-predictions">
                {recentPredictions.map((prediction, index) => (
                  <div key={index} className="recent-prediction-item">
                    <div className="prediction-date">
                      <span className="date">{formatDate(prediction.date)}</span>
                      <span className="time">{prediction.timestamp.toLocaleTimeString()}</span>
                    </div>
                    <div className="prediction-result">
                      <span className="prediction-label">Résultat:</span>
                      <span className="prediction-type">{getLatestPrediction(prediction)}</span>
                    </div>
                  </div>
                ))}
                <Link to="/History" className="view-all-link">
                  Voir tout l'historique
                </Link>
              </div>
            ) : (
              <p className="no-predictions">Aucune classification récente trouvée.</p>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Home;

import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { auth, db } from '../../firebase';
import './Profile.css';

const Profile = () => {
  const [user, setUser] = useState(null);
  const [userData, setUserData] = useState({
    name: '',
    email: '',
    role: ''
  });
  const [isEditing, setIsEditing] = useState(false);
  const [newName, setNewName] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const navigate = useNavigate();

  useEffect(() => {
    const unsubscribe = auth.onAuthStateChanged(async (user) => {
      if (user) {
        setUser(user);
        try {
          const userDoc = await db.collection('users').doc(user.uid).get();
          if (userDoc.exists) {
            const data = userDoc.data();
            setUserData({
              name: data.name || '',
              email: data.email || user.email,
              role: data.role || 'employee'
            });
            setNewName(data.name || '');
          }
        } catch (err) {
          console.error("Error fetching user data:", err);
          setError("Erreur lors de la récupération des données utilisateur.");
        }
      } else {
        // User is not logged in, redirect to login
        navigate('/');
      }
      setLoading(false);
    });

    return () => unsubscribe();
  }, [navigate]);

  const handleEditToggle = () => {
    setIsEditing(!isEditing);
    if (!isEditing) {
      setNewName(userData.name);
    }
  };

  const handleSaveProfile = async () => {
    try {
      setLoading(true);
      await db.collection('users').doc(user.uid).update({
        name: newName
      });
      
      setUserData({
        ...userData,
        name: newName
      });
      
      setIsEditing(false);
      setError('');
    } catch (err) {
      console.error("Error updating profile:", err);
      setError("Erreur lors de la mise à jour du profil.");
    } finally {
      setLoading(false);
    }
  };

  const handlePasswordReset = async () => {
    try {
      await auth.sendPasswordResetEmail(userData.email);
      alert("Un email de réinitialisation de mot de passe a été envoyé à votre adresse email.");
    } catch (err) {
      console.error("Error sending password reset email:", err);
      setError("Erreur lors de l'envoi de l'email de réinitialisation.");
    }
  };

  if (loading) {
    return <div className="profile-loading">Chargement du profil...</div>;
  }

  return (
    <div className="profile-container">
      <div className="profile-card">
        <div className="profile-header">
          <h2>Profil Utilisateur</h2>
          <button 
            className={`profile-edit-btn ${isEditing ? 'cancel' : ''}`} 
            onClick={handleEditToggle}
          >
            {isEditing ? 'Annuler' : 'Modifier'}
          </button>
        </div>

        {error && <div className="profile-error">{error}</div>}

        <div className="profile-content">
          <div className="profile-avatar">
            <div className="avatar-circle">
              {userData.name ? userData.name.charAt(0).toUpperCase() : 'U'}
            </div>
          </div>

          <div className="profile-details">
            <div className="profile-field">
              <label>Nom:</label>
              {isEditing ? (
                <input 
                  type="text" 
                  value={newName} 
                  onChange={(e) => setNewName(e.target.value)} 
                  className="profile-input"
                />
              ) : (
                <span>{userData.name}</span>
              )}
            </div>

            <div className="profile-field">
              <label>Email:</label>
              <span>{userData.email}</span>
            </div>

            <div className="profile-field">
              <label>Rôle:</label>
              <span className="profile-role">{userData.role === 'admin' ? 'Administrateur' : 'Employé'}</span>
            </div>

            {isEditing && (
              <button className="profile-save-btn" onClick={handleSaveProfile}>
                Enregistrer les modifications
              </button>
            )}

            <button className="profile-reset-password" onClick={handlePasswordReset}>
              Réinitialiser le mot de passe
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Profile;

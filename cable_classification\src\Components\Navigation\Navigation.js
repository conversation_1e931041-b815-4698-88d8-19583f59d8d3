import React, { useState, useEffect } from 'react';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import { auth } from '../../firebase';
import './Navigation.css';

const Navigation = () => {
  const [user, setUser] = useState(null);
  const [userRole, setUserRole] = useState(null);
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [showScrollTop, setShowScrollTop] = useState(false);
  const [isOnline, setIsOnline] = useState(window.navigator.onLine);
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    // Check if user is logged in
    let unsubscribe = () => {}; // Initialize with empty function

    try {
      unsubscribe = auth.onAuthStateChanged(async (user) => {
        if (user) {
          setUser(user);

          // Get user role from Firestore
          try {
            const userDoc = await auth.app.firestore().collection('users').doc(user.uid).get();
            if (userDoc.exists) {
              setUserRole(userDoc.data().role);
            } else {
              // If user document doesn't exist, set a default role
              setUserRole('user');
              console.log("User document doesn't exist, using default role");
            }
          } catch (error) {
            // Handle Firestore errors gracefully
            console.error("Error getting user role:", error);

            // Set a default role when offline
            if (error.code === 'failed-precondition' || error.message.includes('offline')) {
              console.log("Using default role due to offline status");
              setUserRole('user'); // Default role when offline
            }
          }
        } else {
          setUser(null);
          setUserRole(null);
        }
      });
    } catch (authError) {
      console.error("Error setting up auth listener:", authError);
      // Continue with the app even if auth fails
    }

    return () => {
      try {
        unsubscribe();
      } catch (error) {
        console.error("Error unsubscribing from auth:", error);
      }
    };
  }, []);

  // Add scroll event listener to show/hide scroll-to-top button
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 300) {
        setShowScrollTop(true);
      } else {
        setShowScrollTop(false);
      }
    };

    window.addEventListener('scroll', handleScroll);

    // Clean up event listener
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  // Monitor online/offline status
  useEffect(() => {
    const handleOnline = () => {
      console.log("App is online");
      setIsOnline(true);
    };

    const handleOffline = () => {
      console.log("App is offline");
      setIsOnline(false);
    };

    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);

    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, []);

  const handleLogout = async () => {
    try {
      await auth.signOut();
      navigate('/');
    } catch (error) {
      console.error("Error signing out:", error);
    }
  };

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const scrollToTop = () => {
    window.scrollTo({
      top: 0,
      behavior: 'smooth'
    });
  };

  // Check if user is admin
  const isAdmin = userRole === 'admin' || userRole === 'administrator' || userRole === 'super_admin';

  // Don't show navigation on login and signup pages
  if (location.pathname === '/' || location.pathname === '/SignUp') {
    return null;
  }

  return (
    <>
      <nav className="navigation">
        {!isOnline && (
          <div className="offline-indicator">
            Mode hors ligne - Certaines fonctionnalités peuvent être limitées
          </div>
        )}
        <div className="nav-container">
          <div className="nav-logo">
            <img src="/cable.png" alt="Logo" className="nav-logo-img" />
            <span className="nav-logo-text">Cable Classification</span>
            {!isOnline && (
              <span className="connection-status offline">⚠️ Hors ligne</span>
            )}
            {isOnline && (
              <span className="connection-status online">✓ En ligne</span>
            )}
          </div>

          <div className="nav-mobile-toggle" onClick={toggleMenu}>
            <span></span>
            <span></span>
            <span></span>
          </div>

          <ul className={`nav-links ${isMenuOpen ? 'active' : ''}`}>
            <li className={location.pathname === '/Home' ? 'active' : ''}>
              <Link to="/Home">
                <span className="nav-link-text">Accueil</span>
                <span className="nav-link-icon">🏠</span>
              </Link>
            </li>

            {/* <li className={location.pathname === '/Classification' ? 'active' : ''}>
              <Link to="/Classification">
                <span className="nav-link-text">Classification</span>
                <span className="nav-link-icon">📊</span>
              </Link>
            </li> */}

            <li className={location.pathname === '/GeminiWebcam' ? 'active' : ''}>
              <Link to="/GeminiWebcam">
                <span className="nav-link-text">Classification</span>
                <span className="nav-link-icon">🤖</span>
              </Link>
            </li>

            <li className={location.pathname === '/History' ? 'active' : ''}>
              <Link to="/History">
                <span className="nav-link-text">Historique</span>
                <span className="nav-link-icon">📋</span>
              </Link>
            </li>

            <li className={location.pathname === '/Profile' ? 'active' : ''}>
              <Link to="/Profile">
                <span className="nav-link-text">Profil</span>
                <span className="nav-link-icon">⚙️</span>
              </Link>
            </li>

            {/* Admin Routes - Only show for admin users */}
            {isAdmin && (
              <>
                <li className={location.pathname === '/Admin' ? 'active admin-route' : 'admin-route'}>
                  <Link to="/Admin">
                    <span className="nav-link-text">Tableau de bord</span>
                    <span className="nav-link-icon">📈</span>
                  </Link>
                </li>

                <li className={location.pathname === '/AdminK' ? 'active admin-route' : 'admin-route'}>
                  <Link to="/AdminK">
                    <span className="nav-link-text">Statistiques</span>
                    <span className="nav-link-icon">📊</span>
                  </Link>
                </li>
              </>
            )}

            {user && (
              <li className="nav-logout">
                <button onClick={handleLogout} className="logout-btn">
                  <span className="nav-link-text">Déconnexion</span>
                  <span className="nav-link-icon">🚪</span>
                </button>
              </li>
            )}
          </ul>
        </div>
      </nav>

      {/* Scroll to top button - Only show when scrolled down */}
      {showScrollTop && (
        <button
          className="scroll-to-top visible"
          onClick={scrollToTop}
          aria-label="Retour en haut"
          title="Retour en haut"
        >
          <span className="scroll-arrow">↑</span>
        </button>
      )}
    </>
  );
};

export default Navigation;
# Dependencies
/node_modules
/.pnp
.pnp.js

# Testing
/coverage

# Production
/build

# Misc
.DS_Store
.env.local
.env.development.local
.env.test.local
.env.production.local

npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
*.egg-info/
.installed.cfg
*.egg

# Virtual Environment
venv/
ENV/
tf_env/

# Model files
*.h5
*.hdf5
*.keras
*.pkl

# Data
data/
data_huggingface/
uploads/
models/

# IDE
.idea/
.vscode/
*.swp
*.swo

# Firebase
firebase-debug.log
firebase-debug.*.log

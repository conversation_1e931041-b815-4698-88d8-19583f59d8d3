
import firebase from 'firebase/compat/app';
import 'firebase/compat/auth';
import 'firebase/compat/firestore';

const firebaseConfig = {
  apiKey: "AIzaSyAGdy-iu0n-1JPMxxB902KX4ZP03Kib-80",
  authDomain: "cableclassification.firebaseapp.com",
  projectId: "cableclassification",
  storageBucket: "cableclassification.appspot.com",
  messagingSenderId: "306632097904",
  appId: "1:306632097904:web:d9e635478eb8d5cefff4b5"
};

// Initialize Firebase
firebase.initializeApp(firebaseConfig);

const auth = firebase.auth();
const db = firebase.firestore();

// Enable offline persistence
db.enablePersistence({ synchronizeTabs: true })
  .then(() => {
    console.log("Firestore persistence enabled");
  })
  .catch((err) => {
    if (err.code === 'failed-precondition') {
      // Multiple tabs open, persistence can only be enabled in one tab at a time
      console.warn("Firestore persistence failed: Multiple tabs open");
    } else if (err.code === 'unimplemented') {
      // The current browser does not support all of the features required for persistence
      console.warn("Firestore persistence not supported by this browser");
    } else {
      console.error("Error enabling Firestore persistence:", err);
    }
  });

const serverTimestamp = firebase.firestore.FieldValue.serverTimestamp;

export {
  auth,
  db,
  firebase,
  serverTimestamp
};
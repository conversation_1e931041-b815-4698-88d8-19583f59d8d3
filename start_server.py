"""
Script pour démarrer le serveur backend.
"""

import os
import sys
import subprocess
import time
import webbrowser

def print_header(message):
    """Affiche un message d'en-tête formaté"""
    print("\n" + "=" * 80)
    print(f"  {message}")
    print("=" * 80 + "\n")

def run_command(command, description):
    """Exécute une commande et affiche sa sortie"""
    print_header(description)
    print(f"Exécution de: {command}")
    
    try:
        process = subprocess.Popen(
            command,
            shell=True,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True
        )
        
        # Afficher la sortie en temps réel
        for line in process.stdout:
            print(line, end='')
        
        process.wait()
        
        if process.returncode != 0:
            print(f"\nErreur: La commande a échoué avec le code de retour {process.returncode}")
            return False
        
        print("\nCommande exécutée avec succès!")
        return True
    
    except Exception as e:
        print(f"\nException lors de l'exécution de la commande: {str(e)}")
        return False

def check_dependencies():
    """Vérifie si les dépendances sont installées"""
    print_header("VÉRIFICATION DES DÉPENDANCES")
    
    # Liste des dépendances requises
    dependencies = [
        "flask",
        "flask-cors",
        "pillow",
        "tensorflow",
        "numpy"
    ]
    
    # Vérifier chaque dépendance
    missing_deps = []
    for dep in dependencies:
        try:
            __import__(dep.replace("-", "_"))
            print(f"✓ {dep} est installé")
        except ImportError:
            print(f"✗ {dep} n'est pas installé")
            missing_deps.append(dep)
    
    # Installer les dépendances manquantes
    if missing_deps:
        print("\nInstallation des dépendances manquantes...")
        deps_str = " ".join(missing_deps)
        if not run_command(f"pip install {deps_str}", "INSTALLATION DES DÉPENDANCES"):
            print("Erreur lors de l'installation des dépendances.")
            return False
    
    return True

def start_server():
    """Démarre le serveur backend"""
    print_header("DÉMARRAGE DU SERVEUR BACKEND")
    
    # Vérifier si le modèle existe
    model_path = os.path.join("backend", "cable_classifier_final.keras")
    if not os.path.exists(model_path):
        print(f"Erreur: Le modèle {model_path} n'existe pas.")
        return False
    
    # Démarrer le serveur
    server_command = "cd backend && python app.py"
    print(f"Démarrage du serveur avec la commande: {server_command}")
    print("Le serveur sera accessible à l'adresse: http://localhost:5000")
    print("\nAppuyez sur Ctrl+C pour arrêter le serveur.")
    
    # Ouvrir le navigateur
    webbrowser.open("http://localhost:5000")
    
    # Exécuter la commande
    try:
        subprocess.run(server_command, shell=True)
        return True
    except KeyboardInterrupt:
        print("\nServeur arrêté par l'utilisateur.")
        return True
    except Exception as e:
        print(f"\nErreur lors du démarrage du serveur: {str(e)}")
        return False

def main():
    """Fonction principale"""
    print_header("DÉMARRAGE DE L'APPLICATION DE CLASSIFICATION DE CÂBLES")
    
    # Vérifier les dépendances
    if not check_dependencies():
        print("Erreur: Impossible de vérifier ou d'installer les dépendances.")
        return
    
    # Démarrer le serveur
    if not start_server():
        print("Erreur: Impossible de démarrer le serveur.")
        return
    
    print_header("SERVEUR ARRÊTÉ")

if __name__ == "__main__":
    main()

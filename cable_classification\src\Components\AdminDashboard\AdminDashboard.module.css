/* Enhanced AdminDashboard.module.css - 10x Better */
:root {
  /* Admin Color Palette */
  --admin-primary: #2563eb;
  --admin-primary-dark: #1d4ed8;
  --admin-primary-light: #3b82f6;
  --admin-secondary: #7c3aed;
  --admin-accent: #06b6d4;
  --admin-success: #059669;
  --admin-warning: #d97706;
  --admin-error: #dc2626;
  --admin-info: #0891b2;
  
  /* Gradients */
  --admin-gradient-primary: linear-gradient(135deg, #2563eb 0%, #7c3aed 100%);
  --admin-gradient-success: linear-gradient(135deg, #059669 0%, #10b981 100%);
  --admin-gradient-warning: linear-gradient(135deg, #d97706 0%, #f59e0b 100%);
  --admin-gradient-error: linear-gradient(135deg, #dc2626 0%, #ef4444 100%);
  --admin-gradient-info: linear-gradient(135deg, #0891b2 0%, #06b6d4 100%);
  
  /* Neutral Colors */
  --admin-white: #ffffff;
  --admin-gray-50: #f8fafc;
  --admin-gray-100: #f1f5f9;
  --admin-gray-200: #e2e8f0;
  --admin-gray-300: #cbd5e1;
  --admin-gray-400: #94a3b8;
  --admin-gray-500: #64748b;
  --admin-gray-600: #475569;
  --admin-gray-700: #334155;
  --admin-gray-800: #1e293b;
  --admin-gray-900: #0f172a;
  
  /* Shadows */
  --admin-shadow-subtle: 0 1px 2px rgba(0, 0, 0, 0.05);
  --admin-shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  --admin-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --admin-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --admin-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --admin-shadow-2xl: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  --admin-shadow-glow: 0 0 40px rgba(37, 99, 235, 0.2);
  
  /* Border Radius */
  --admin-radius-sm: 0.375rem;
  --admin-radius-md: 0.5rem;
  --admin-radius-lg: 0.75rem;
  --admin-radius-xl: 1rem;
  --admin-radius-2xl: 1.5rem;
  --admin-radius-3xl: 2rem;
  --admin-radius-full: 9999px;
  
  /* Transitions */
  --admin-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --admin-transition-bounce: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 50%, #f1f5f9 100%);
  padding: 2rem;
  font-family: 'Inter', system-ui, -apple-system, sans-serif;
  position: relative;
  overflow-x: hidden;
}

.container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 20%, rgba(37, 99, 235, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 80% 80%, rgba(124, 58, 237, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 50% 50%, rgba(5, 150, 105, 0.03) 0%, transparent 50%);
  animation: backgroundShift 20s ease-in-out infinite;
  z-index: 0;
}

@keyframes backgroundShift {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.container > * {
  position: relative;
  z-index: 1;
}

.header {
  text-align: center;
  margin-bottom: 3rem;
  animation: headerSlideDown 0.8s ease-out;
}

@keyframes headerSlideDown {
  from {
    opacity: 0;
    transform: translateY(-50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.title {
  font-size: clamp(2.5rem, 6vw, 4rem);
  font-weight: 900;
  background: var(--admin-gradient-primary);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 1rem;
  position: relative;
  display: inline-block;
}

.title::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: var(--admin-gradient-primary);
  border-radius: var(--admin-radius-full);
  animation: underlineGrow 1s ease-out 0.5s both;
}

@keyframes underlineGrow {
  from { width: 0; }
  to { width: 80px; }
}

.subtitle {
  font-size: 1.25rem;
  color: var(--admin-gray-600);
  font-weight: 500;
  margin: 0;
  animation: subtitleFade 1s ease-out 0.3s both;
}

@keyframes subtitleFade {
  from { opacity: 0; }
  to { opacity: 1; }
}

.loading {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  text-align: center;
}

.spinner {
  width: 60px;
  height: 60px;
  border: 4px solid var(--admin-gray-200);
  border-top: 4px solid var(--admin-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 2rem;
  box-shadow: var(--admin-shadow-md);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading p {
  font-size: 1.25rem;
  color: var(--admin-gray-600);
  font-weight: 500;
}

.error {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  text-align: center;
  color: var(--admin-error);
}

.error p {
  font-size: 1.25rem;
  margin-bottom: 2rem;
  font-weight: 600;
}

.retryButton {
  background: var(--admin-gradient-primary);
  color: var(--admin-white);
  border: none;
  padding: 1rem 2rem;
  border-radius: var(--admin-radius-lg);
  font-size: 1.1rem;
  font-weight: 600;
  cursor: pointer;
  transition: var(--admin-transition-bounce);
  box-shadow: var(--admin-shadow-md);
}

.retryButton:hover {
  transform: translateY(-3px) scale(1.05);
  box-shadow: var(--admin-shadow-lg), var(--admin-shadow-glow);
}

.summaryCards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
  animation: cardsSlideUp 0.8s ease-out;
}

@keyframes cardsSlideUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.summaryCard {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(15px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: var(--admin-radius-2xl);
  padding: 2rem;
  text-align: center;
  box-shadow: var(--admin-shadow-md);
  transition: var(--admin-transition);
  position: relative;
  overflow: hidden;
  animation: cardFloat 0.8s ease-out;
  animation-fill-mode: both;
}

.summaryCard:nth-child(1) { animation-delay: 0.1s; }
.summaryCard:nth-child(2) { animation-delay: 0.2s; }
.summaryCard:nth-child(3) { animation-delay: 0.3s; }
.summaryCard:nth-child(4) { animation-delay: 0.4s; }

@keyframes cardFloat {
  from {
    opacity: 0;
    transform: translateY(30px) rotateX(20deg);
  }
  to {
    opacity: 1;
    transform: translateY(0) rotateX(0);
  }
}

.summaryCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--card-gradient, var(--admin-gradient-primary));
  border-radius: var(--admin-radius-2xl) var(--admin-radius-2xl) 0 0;
}

.summaryCard:hover {
  transform: translateY(-8px) rotateX(5deg);
  box-shadow: var(--admin-shadow-xl);
}

.summaryCard h3 {
  font-size: 0.875rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  color: var(--admin-gray-600);
  margin-bottom: 1.5rem;
  position: relative;
}

.totalNumber {
  font-size: 3.5rem;
  font-weight: 900;
  color: var(--admin-primary);
  margin-bottom: 0.5rem;
  line-height: 1;
  text-shadow: 0 2px 4px rgba(37, 99, 235, 0.1);
}

.calageNumber {
  font-size: 3.5rem;
  font-weight: 900;
  color: var(--admin-warning);
  margin-bottom: 0.5rem;
  line-height: 1;
  text-shadow: 0 2px 4px rgba(217, 119, 6, 0.1);
}

.nooNumber {
  font-size: 3.5rem;
  font-weight: 900;
  color: var(--admin-error);
  margin-bottom: 0.5rem;
  line-height: 1;
  text-shadow: 0 2px 4px rgba(220, 38, 38, 0.1);
}

.parfaitNumber {
  font-size: 3.5rem;
  font-weight: 900;
  color: var(--admin-success);
  margin-bottom: 0.5rem;
  line-height: 1;
  text-shadow: 0 2px 4px rgba(5, 150, 105, 0.1);
}

.summaryCard:nth-child(1) { --card-gradient: var(--admin-gradient-info); }
.summaryCard:nth-child(2) { --card-gradient: var(--admin-gradient-warning); }
.summaryCard:nth-child(3) { --card-gradient: var(--admin-gradient-error); }
.summaryCard:nth-child(4) { --card-gradient: var(--admin-gradient-success); }

.percentage {
  font-size: 1rem;
  color: var(--admin-gray-500);
  font-weight: 600;
  margin: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.percentage::before {
  content: '📊';
  font-size: 1.2rem;
}

.statsContainer {
  margin-bottom: 3rem;
  animation: statsSlideIn 1s ease-out 0.5s both;
}

@keyframes statsSlideIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.sectionTitle {
  font-size: 2.25rem;
  font-weight: 800;
  background: var(--admin-gradient-primary);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-align: center;
  margin-bottom: 2.5rem;
  position: relative;
}

.sectionTitle::before {
  content: '📈';
  position: absolute;
  left: -50px;
  top: 50%;
  transform: translateY(-50%);
  font-size: 2rem;
  animation: iconBounce 2s ease-in-out infinite;
}

@keyframes iconBounce {
  0%, 20%, 50%, 80%, 100% { transform: translateY(-50%); }
  40% { transform: translateY(-70%); }
  60% { transform: translateY(-65%); }
}

.statsGrid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 2rem;
}

.statCard {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: var(--admin-radius-xl);
  padding: 2rem;
  box-shadow: var(--admin-shadow-md);
  transition: var(--admin-transition);
  position: relative;
  overflow: hidden;
}

.statCard::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--admin-primary), var(--admin-accent));
  border-radius: var(--admin-radius-xl) var(--admin-radius-xl) 0 0;
}

.statCard:hover {
  transform: translateY(-5px) scale(1.02);
  box-shadow: var(--admin-shadow-xl);
}

.statCard:hover::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, rgba(37, 99, 235, 0.05), rgba(124, 58, 237, 0.05));
  border-radius: var(--admin-radius-xl);
  pointer-events: none;
}

.date {
  font-size: 1.5rem;
  font-weight: 700;
  background: var(--admin-gradient-primary);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-align: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 2px solid var(--admin-gray-100);
  position: relative;
}

.date::after {
  content: '📅';
  position: absolute;
  right: 0;
  top: 0;
  font-size: 1.2rem;
}

.statsList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.statItem {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  background: var(--admin-gray-50);
  border-radius: var(--admin-radius-lg);
  transition: var(--admin-transition);
  border: 1px solid transparent;
}

.statItem:hover {
  background: var(--admin-white);
  border-color: var(--admin-primary);
  transform: scale(1.02);
  box-shadow: var(--admin-shadow-sm);
}

.statItem.total {
  background: linear-gradient(135deg, var(--admin-primary), var(--admin-secondary));
  color: var(--admin-white);
  font-weight: 700;
  margin-top: 0.5rem;
  box-shadow: var(--admin-shadow-md);
}

.statItem.total:hover {
  background: linear-gradient(135deg, var(--admin-primary-dark), var(--admin-secondary));
  box-shadow: var(--admin-shadow-lg);
}

.statLabel {
  font-size: 1rem;
  font-weight: 600;
  color: inherit;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.statLabel::before {
  content: var(--stat-icon, '🔗');
  font-size: 1.2rem;
}

.statItem:nth-child(1) .statLabel { --stat-icon: '⚠️'; }
.statItem:nth-child(2) .statLabel { --stat-icon: '❌'; }
.statItem:nth-child(3) .statLabel { --stat-icon: '✅'; }
.statItem.total .statLabel { --stat-icon: '📊'; }

.statValue {
  font-size: 1.5rem;
  font-weight: 800;
  color: inherit;
  background: rgba(255, 255, 255, 0.1);
  padding: 0.5rem 1rem;
  border-radius: var(--admin-radius-md);
  min-width: 60px;
  text-align: center;
}

.statItem.total .statValue {
  background: rgba(255, 255, 255, 0.2);
}

.lastUpdated {
  font-size: 0.875rem;
  color: var(--admin-gray-500);
  text-align: center;
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid var(--admin-gray-200);
  font-style: italic;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
}

.lastUpdated::before {
  content: '🕒';
  font-size: 1rem;
}

.noData {
  text-align: center;
  padding: 4rem 2rem;
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: var(--admin-radius-2xl);
  box-shadow: var(--admin-shadow-lg);
  border: 1px solid var(--admin-gray-200);
}

.noData h2 {
  font-size: 2rem;
  color: var(--admin-gray-600);
  margin-bottom: 1rem;
  font-weight: 700;
}

.noData p {
  color: var(--admin-gray-500);
  font-size: 1.1rem;
  font-weight: 500;
}

.noData::before {
  content: '📊';
  font-size: 4rem;
  display: block;
  margin-bottom: 1.5rem;
  opacity: 0.5;
}

.recentActivity {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(15px);
  border-radius: var(--admin-radius-2xl);
  padding: 2.5rem;
  box-shadow: var(--admin-shadow-lg);
  border: 1px solid rgba(255, 255, 255, 0.3);
  position: relative;
  overflow: hidden;
  animation: activitySlideIn 1s ease-out 0.7s both;
}

@keyframes activitySlideIn {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.recentActivity::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--admin-gradient-info);
  border-radius: var(--admin-radius-2xl) var(--admin-radius-2xl) 0 0;
}

.recentActivity .sectionTitle::before {
  content: '⚡';
  left: -45px;
}

.activityList {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.activityItem {
  display: grid;
  grid-template-columns: auto auto 1fr;
  gap: 1.5rem;
  padding: 1.5rem;
  background: linear-gradient(135deg, var(--admin-gray-50), var(--admin-white));
  border-radius: var(--admin-radius-lg);
  align-items: center;
  transition: var(--admin-transition);
  border: 1px solid var(--admin-gray-200);
  position: relative;
  overflow: hidden;
}

.activityItem::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: linear-gradient(180deg, var(--admin-primary), var(--admin-accent));
  border-radius: 0 var(--admin-radius-md) var(--admin-radius-md) 0;
}

.activityItem:hover {
  background: linear-gradient(135deg, var(--admin-primary), var(--admin-secondary));
  color: var(--admin-white);
  transform: translateX(10px);
  box-shadow: var(--admin-shadow-md);
}

.activityDate {
  font-size: 0.875rem;
  font-weight: 700;
  color: var(--admin-primary);
  background: rgba(37, 99, 235, 0.1);
  padding: 0.5rem 1rem;
  border-radius: var(--admin-radius-md);
  white-space: nowrap;
}

.activityItem:hover .activityDate {
  color: var(--admin-white);
  background: rgba(255, 255, 255, 0.2);
}

.activityTime {
  font-size: 0.875rem;
  color: var(--admin-gray-600);
  font-weight: 600;
  background: var(--admin-gray-100);
  padding: 0.5rem 1rem;
  border-radius: var(--admin-radius-md);
  white-space: nowrap;
}

.activityItem:hover .activityTime {
  color: var(--admin-white);
  background: rgba(255, 255, 255, 0.2);
}

.activityDescription {
  font-size: 1rem;
  color: var(--admin-gray-700);
  font-weight: 500;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.activityDescription::before {
  content: '🔄';
  font-size: 1.2rem;
}

.activityItem:hover .activityDescription {
  color: var(--admin-white);
}

/* Responsive Design */
@media (max-width: 1024px) {
  .container {
    padding: 1.5rem;
  }
  
  .summaryCards {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1.5rem;
  }
  
  .statsGrid {
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 1rem;
  }
  
  .header {
    margin-bottom: 2rem;
  }
  
  .title {
    font-size: 2.5rem;
  }
  
  .summaryCards {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .statsGrid {
    grid-template-columns: 1fr;
    gap: 1rem;
  }
  
  .recentActivity {
    padding: 1.5rem;
  }
  
  .activityItem {
    grid-template-columns: 1fr;
    gap: 0.75rem;
    text-align: center;
  }
  
  .activityDate,
  .activityTime,
  .activityDescription {
    justify-self: center;
  }
  
  .sectionTitle::before {
    display: none;
  }
  
  .totalNumber,
  .calageNumber,
  .nooNumber,
  .parfaitNumber {
    font-size: 2.5rem;
  }
  
  .summaryCard h3 {
    font-size: 0.75rem;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0.75rem;
  }
  
  .summaryCard {
    padding: 1.5rem;
  }
  
  .statCard {
    padding: 1.5rem;
  }
  
  .recentActivity {
    padding: 1rem;
  }
  
  .totalNumber,
  .calageNumber,
  .nooNumber,
  .parfaitNumber {
    font-size: 2rem;
  }
  
  .date {
    font-size: 1.25rem;
  }
  
  .statValue {
    font-size: 1.25rem;
  }
}

/* Dark Theme Support */
@media (prefers-color-scheme: dark) {
  .container {
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 50%, #0f172a 100%);
  }
  
  .summaryCard,
  .statCard,
  .recentActivity {
    background: rgba(15, 23, 42, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .noData {
    background: rgba(15, 23, 42, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .statItem {
    background: rgba(30, 41, 59, 0.5);
    color: var(--admin-white);
  }
  
  .statItem:hover {
    background: rgba(37, 99, 235, 0.2);
  }
  
  .activityItem {
    background: rgba(30, 41, 59, 0.5);
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: var(--admin-white);
  }
  
  .activityDate {
    background: rgba(37, 99, 235, 0.2);
  }
  
  .activityTime {
    background: rgba(100, 116, 139, 0.2);
  }
  
  .subtitle {
    color: var(--admin-gray-400);
  }
  
  .noData h2 {
    color: var(--admin-gray-300);
  }
  
  .noData p {
    color: var(--admin-gray-400);
  }
}

/* High Performance Animations */
.summaryCard,
.statCard,
.activityItem {
  will-change: transform;
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Print Styles */
@media print {
  .container {
    background: white;
    color: black;
  }
  
  .summaryCard,
  .statCard,
  .recentActivity {
    background: white;
    border: 1px solid #ccc;
    box-shadow: none;
  }
  
  .container::before {
    display: none;
  }
  
  .spinner {
    display: none;
  }
}

/* Custom Properties for Dynamic Theming */
.theme-blue {
  --admin-primary: #2563eb;
  --admin-accent: #06b6d4;
}

.theme-purple {
  --admin-primary: #7c3aed;
  --admin-accent: #a855f7;
}

.theme-green {
  --admin-primary: #059669;
  --admin-accent: #10b981;
}

/* Loading States */
.loading-card {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

/* Focus States */
.retryButton:focus-visible,
.activityItem:focus-visible {
  outline: 3px solid var(--admin-accent);
  outline-offset: 3px;
}

/* Success States */
.success-animation {
  animation: successPulse 0.6s ease-in-out;
}

@keyframes successPulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

/* Status Indicators */
.status-indicator {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: var(--admin-radius-full);
  font-size: 0.875rem;
  font-weight: 600;
}

.status-online {
  background: rgba(5, 150, 105, 0.1);
  color: var(--admin-success);
  border: 1px solid rgba(5, 150, 105, 0.2);
}

.status-offline {
  background: rgba(220, 38, 38, 0.1);
  color: var(--admin-error);
  border: 1px solid rgba(220, 38, 38, 0.2);
}

.status-pending {
  background: rgba(217, 119, 6, 0.1);
  color: var(--admin-warning);
  border: 1px solid rgba(217, 119, 6, 0.2);
}

/* Notification Styles */
.notification {
  position: fixed;
  top: 2rem;
  right: 2rem;
  background: var(--admin-white);
  border-radius: var(--admin-radius-lg);
  padding: 1.5rem;
  box-shadow: var(--admin-shadow-xl);
  border-left: 4px solid var(--admin-success);
  z-index: 1000;
  animation: notificationSlide 0.5s ease-out;
}

@keyframes notificationSlide {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
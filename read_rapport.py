#!/usr/bin/env python3
"""
<PERSON>ript to read the Word document rapport and save content to text file
"""

try:
    import docx

    def read_docx(file_path):
        """Read content from a Word document"""
        try:
            doc = docx.Document(file_path)
            content = []

            for paragraph in doc.paragraphs:
                text = paragraph.text.strip()
                if text:
                    content.append(text)

            return '\n'.join(content)
        except Exception as e:
            return f"Error reading document: {e}"

    # Read the rapport
    rapport_content = read_docx('rapport/rapport pfe(1).docx')

    # Save to text file for easier processing
    with open('rapport_content.txt', 'w', encoding='utf-8') as f:
        f.write(rapport_content)

    print("Rapport content saved to rapport_content.txt")
    print("\n" + "="*50)
    print("RAPPORT CONTENT:")
    print("="*50)
    print(rapport_content)

except ImportError:
    print("python-docx not installed. Installing...")
    import subprocess
    import sys
    subprocess.check_call([sys.executable, "-m", "pip", "install", "python-docx"])

    # Try again after installation
    import docx

    def read_docx(file_path):
        """Read content from a Word document"""
        try:
            doc = docx.Document(file_path)
            content = []

            for paragraph in doc.paragraphs:
                text = paragraph.text.strip()
                if text:
                    content.append(text)

            return '\n'.join(content)
        except Exception as e:
            return f"Error reading document: {e}"

    # Read the rapport
    rapport_content = read_docx('rapport/rapport pfe(1).docx')

    # Save to text file for easier processing
    with open('rapport_content.txt', 'w', encoding='utf-8') as f:
        f.write(rapport_content)

    print("Rapport content saved to rapport_content.txt")
    print("\n" + "="*50)
    print("RAPPORT CONTENT:")
    print("="*50)
    print(rapport_content)

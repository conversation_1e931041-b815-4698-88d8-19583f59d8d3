"""
Script pour ouvrir l'explorateur de fichiers à l'emplacement des fichiers préparés.
"""

import os
import subprocess
import sys

def open_folder(path):
    """Ouvre l'explorateur de fichiers à l'emplacement spécifié"""
    if sys.platform == 'win32':
        os.startfile(path)
    elif sys.platform == 'darwin':  # macOS
        subprocess.call(['open', path])
    else:  # linux
        subprocess.call(['xdg-open', path])

if __name__ == "__main__":
    # Chemin vers le dossier colab
    script_dir = os.path.dirname(os.path.abspath(__file__))
    colab_dir = os.path.join(script_dir, 'backend', 'colab')
    
    # Vérifier si le dossier existe
    if os.path.exists(colab_dir):
        print(f"Ouverture du dossier: {colab_dir}")
        open_folder(colab_dir)
    else:
        print(f"Erreur: Le dossier {colab_dir} n'existe pas.")

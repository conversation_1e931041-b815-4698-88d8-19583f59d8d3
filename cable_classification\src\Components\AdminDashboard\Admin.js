// src/components/AdminDashboard.js
import React, { useState, useEffect } from 'react';
import { db } from '../../firebase';
import styles from './AdminDashboard.module.css'; // Créez ce fichier CSS

function AdminDashboard() {
    const [dailyStats, setDailyStats] = useState({});
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState(null);

    useEffect(() => {
        const fetchDailyStats = async () => {
            setLoading(true);
            setError(null);
            try {
                const predictionsCollection = db.collection('types_cables');
                const querySnapshot = await predictionsCollection.get();
                const stats = {};
                querySnapshot.forEach(doc => {
                    stats[doc.id] = doc.data();
                });
                setDailyStats(stats);
            } catch (err) {
                console.error('Erreur lors de la récupération des statistiques:', err);
                setError('Erreur lors du chargement des statistiques.');
            } finally {
                setLoading(false);
            }
        };

        fetchDailyStats();
    }, []);

    if (loading) {
        return <div className={styles.loading}>Chargement des statistiques...</div>;
    }

    if (error) {
        return <div className={styles.error}>Erreur: {error}</div>;
    }

    const formatDate = (dateString) => {
        const parts = dateString.split('-');
        return `${parts[0]}/${parts[1]}/${parts[2]}`;
    };

    return (
        <div className={styles.container}>
            <h2 className={styles.title}>Tableau de Bord d'Administration</h2>
            {Object.keys(dailyStats).length > 0 ? (
                <div className={styles.statsGrid}>
                    {Object.entries(dailyStats).sort((a, b) => new Date(b[0].split('-').reverse().join('-')) - new Date(a[0].split('-').reverse().join('-'))).map(([date, stats]) => (
                        <div key={date} className={styles.statCard}>
                            <h3 className={styles.date}>{formatDate(date)}</h3>
                            <ul className={styles.classList}>
                                {Object.entries(stats).filter(([key]) => key !== 'lastUpdated').map(([className, count]) => (
                                    <li key={className} className={styles.classItem}>
                                        {className} : <span className={styles.count}>{count}</span>
                                    </li>
                                ))}
                            </ul>
                            {stats.lastUpdated && (
                                <p className={styles.lastUpdated}>
                                    Dernière mise à jour :{' '}
                                    {new Date(stats.lastUpdated.toDate()).toLocaleString()}
                                </p>
                            )}
                        </div>
                    ))}
                </div>
            ) : (
                <p className={styles.noData}>Aucune statistique de prédiction enregistrée.</p>
            )}
        </div>
    );
}

export default AdminDashboard;
import React, { useState } from 'react';
import './AdminDashboard.css';
import DatasetViewer from './DatasetViewer';
import ModelPerformance from './ModelPerformance';

function AdminK() {
  const [activeTab, setActiveTab] = useState('dataset');
  
  return (
    <div className="dashboard-container">
      <aside className="sidebar">
        <div className="logo">Admin Panel</div>
        <nav>
          <ul>
            <li>
              <button
                className={activeTab === 'dataset' ? 'active' : ''}
                onClick={() => setActiveTab('dataset')}
              >
                Dataset
              </button>
            </li>
            <li>
              <button
                className={activeTab === 'model' ? 'active' : ''}
                onClick={() => setActiveTab('model')}
              >
                Performance du Modèle
              </button>
            </li>
          </ul>
        </nav>
      </aside>

      <main className="main-content">
        <header>
          <h1>Statistiques et Modèle</h1>
        </header>
        
        {activeTab === 'dataset' && (
          <DatasetViewer />
        )}
        
        {activeTab === 'model' && (
          <ModelPerformance />
        )}
      </main>
    </div>
  );
}

export default AdminK;

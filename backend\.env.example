# Configuration pour l'application de classification de câbles

# Clé API Google Gemini (obligatoire pour utiliser Gemini Vision)
# Obtenez votre clé API sur: https://makersuite.google.com/app/apikey
GEMINI_API_KEY=your_gemini_api_key_here

# Configuration Flask
FLASK_ENV=development
FLASK_DEBUG=True

# Configuration des modèles
DEFAULT_MODEL=gemini_vision  # ou keras_classifier
KERAS_MODEL_PATH=cable_classifier_final.keras

# Configuration des uploads
MAX_UPLOAD_SIZE=16777216  # 16MB en bytes
ALLOWED_EXTENSIONS=png,jpg,jpeg

# Configuration Gemini
GEMINI_MAX_RETRIES=3
GEMINI_TIMEOUT=30

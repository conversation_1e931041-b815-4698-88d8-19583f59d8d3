#!/usr/bin/env python3
"""
🤖 Cable Classification - Gemini Only Clean Server
Serveur Flask simplifié avec seulement Gemini AI pour classification de câbles
"""

import os
import sys
from datetime import datetime
from flask import Flask, request, jsonify
from flask_cors import CORS
from werkzeug.utils import secure_filename
import logging
from dotenv import load_dotenv

# Charger les variables d'environnement depuis le fichier .env
load_dotenv()

# Définir la nouvelle clé API Gemini
os.environ['GEMINI_API_KEY'] = 'AIzaSyAaL0jybRUYfoy4NQDO6MhxIle4si3rtpI'

# Debug: Vérifier si la clé API est chargée
print(f"🔑 GEMINI_API_KEY chargée: {'✅ Oui' if os.getenv('GEMINI_API_KEY') else '❌ Non'}")
if os.getenv('GEMINI_API_KEY'):
    print(f"🔑 Clé API: {os.getenv('GEMINI_API_KEY')[:20]}...")

# Configuration du logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Import Gemini classifier
try:
    from gemini_classifier import GeminiCableClassifier
    logger.info("✅ Gemini classifier imported successfully")
except ImportError as e:
    logger.error(f"❌ Failed to import Gemini classifier: {e}")
    sys.exit(1)

# Configuration Flask
app = Flask(__name__)
CORS(app)

# Configuration
UPLOAD_FOLDER = 'uploads'
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg', 'gif', 'bmp', 'webp'}

# Créer le dossier uploads s'il n'existe pas
os.makedirs(UPLOAD_FOLDER, exist_ok=True)

# Configuration Flask
app.config['UPLOAD_FOLDER'] = UPLOAD_FOLDER
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max

# Initialisation du classificateur Gemini
try:
    gemini_classifier = GeminiCableClassifier()
    logger.info("🤖 Gemini classifier initialized successfully!")
except Exception as e:
    logger.error(f"❌ Failed to initialize Gemini classifier: {e}")
    gemini_classifier = None

def allowed_file(filename):
    """Vérifie si le fichier a une extension autorisée"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def save_uploaded_file(file):
    """Sauvegarde le fichier uploadé et retourne le chemin"""
    if file and allowed_file(file.filename):
        # Créer un nom de fichier unique avec timestamp
        timestamp = datetime.now().strftime("%Y%m%d-%H%M%S")
        filename = f"{timestamp}_{secure_filename(file.filename)}"
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(filepath)
        return filepath
    return None

@app.route('/', methods=['GET'])
def home():
    """Page d'accueil de l'API"""
    return jsonify({
        'message': '🤖 Cable Classification API - Gemini Only',
        'version': '2.0.0',
        'status': 'active',
        'gemini_available': gemini_classifier is not None,
        'endpoints': {
            'webcam_classify': '/api/webcam/classify_gemini',
            'upload_classify': '/api/upload/classify_gemini'
        }
    })

@app.route('/api/webcam/classify_gemini', methods=['POST'])
def classify_webcam_gemini():
    """Classification d'image webcam avec Gemini"""
    try:
        if not gemini_classifier:
            return jsonify({'error': 'Gemini classifier not available'}), 500

        # Vérifier si un fichier est présent
        if 'image' not in request.files:
            return jsonify({'error': 'No image file provided'}), 400

        file = request.files['image']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400

        # Sauvegarder le fichier
        filepath = save_uploaded_file(file)
        if not filepath:
            return jsonify({'error': 'Invalid file format'}), 400

        # Vérifier que le fichier existe et n'est pas vide
        if not os.path.exists(filepath) or os.path.getsize(filepath) == 0:
            return jsonify({'error': 'Image file is empty or corrupted'}), 400

        logger.info(f"📸 Processing webcam image: {filepath} (size: {os.path.getsize(filepath)} bytes)")

        # Classification avec Gemini
        try:
            gemini_results = gemini_classifier.classify_cable(filepath)

            # Nettoyer le fichier temporaire
            if os.path.exists(filepath):
                os.remove(filepath)

            return jsonify({
                'success': True,
                'gemini_results': gemini_results,
                'timestamp': datetime.now().isoformat()
            })

        except Exception as e:
            logger.error(f"❌ Gemini classification error: {e}")
            # Nettoyer le fichier en cas d'erreur
            if os.path.exists(filepath):
                os.remove(filepath)
            return jsonify({'error': f'Gemini classification failed: {str(e)}'}), 500

    except Exception as e:
        logger.error(f"❌ Webcam classification error: {e}")
        return jsonify({'error': f'Server error: {str(e)}'}), 500

@app.route('/api/upload/classify_gemini', methods=['POST'])
def classify_upload_gemini():
    """Classification d'image uploadée avec Gemini"""
    try:
        if not gemini_classifier:
            return jsonify({'error': 'Gemini classifier not available'}), 500

        # Vérifier si un fichier est présent
        if 'image' not in request.files:
            return jsonify({'error': 'No image file provided'}), 400

        file = request.files['image']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400

        # Sauvegarder le fichier
        filepath = save_uploaded_file(file)
        if not filepath:
            return jsonify({'error': 'Invalid file format'}), 400

        # Vérifier que le fichier existe et n'est pas vide
        if not os.path.exists(filepath) or os.path.getsize(filepath) == 0:
            return jsonify({'error': 'Image file is empty or corrupted'}), 400

        logger.info(f"📁 Processing uploaded image: {filepath} (size: {os.path.getsize(filepath)} bytes)")

        # Classification avec Gemini
        try:
            gemini_results = gemini_classifier.classify_cable(filepath)

            return jsonify({
                'success': True,
                'gemini_results': gemini_results,
                'filepath': filepath,
                'timestamp': datetime.now().isoformat()
            })

        except Exception as e:
            logger.error(f"❌ Gemini classification error: {e}")
            return jsonify({'error': f'Gemini classification failed: {str(e)}'}), 500

    except Exception as e:
        logger.error(f"❌ Upload classification error: {e}")
        return jsonify({'error': f'Server error: {str(e)}'}), 500

@app.route('/api/health', methods=['GET'])
def health_check():
    """Vérification de l'état du serveur"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'gemini_available': gemini_classifier is not None
    })

@app.errorhandler(413)
def too_large(e):
    return jsonify({'error': 'File too large. Maximum size is 16MB.'}), 413

@app.errorhandler(404)
def not_found(e):
    return jsonify({'error': 'Endpoint not found'}), 404

@app.errorhandler(500)
def internal_error(e):
    return jsonify({'error': 'Internal server error'}), 500

if __name__ == '__main__':
    print("🚀 Starting Cable Classification Server - Gemini Only")
    print("=" * 60)
    print(f"📊 Gemini Available: {'✅ Yes' if gemini_classifier else '❌ No'}")
    print(f"📁 Upload Folder: {UPLOAD_FOLDER}")
    print(f"🌐 Server URL: http://localhost:5000")
    print("=" * 60)

    app.run(
        host='0.0.0.0',
        port=5000,
        debug=True,
        threaded=True
    )

/* Enhanced App.css with modern design */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

:root {
  /* Primary Colors */
  --primary-blue: #0052cc;
  --primary-blue-light: #2684ff;
  --primary-blue-dark: #003d99;

  /* Success Colors */
  --success-green: #36b37e;
  --success-green-light: #57d9a3;
  --success-green-dark: #006644;

  /* Warning Colors */
  --warning-orange: #ffab00;
  --warning-orange-light: #ffc333;
  --warning-orange-dark: #cc8800;

  /* Error Colors */
  --error-red: #de350b;
  --error-red-light: #ff5630;
  --error-red-dark: #bf2600;

  /* Neutral Colors */
  --neutral-white: #ffffff;
  --neutral-gray-50: #f7f8f9;
  --neutral-gray-100: #ebecf0;
  --neutral-gray-200: #dfe1e6;
  --neutral-gray-300: #c1c7d0;
  --neutral-gray-400: #9fadbc;
  --neutral-gray-500: #7a869a;
  --neutral-gray-600: #5e6c84;
  --neutral-gray-700: #42526e;
  --neutral-gray-800: #253858;
  --neutral-gray-900: #091e42;

  /* Gradients */
  --gradient-primary: linear-gradient(135deg, var(--primary-blue), var(--primary-blue-light));
  --gradient-success: linear-gradient(135deg, var(--success-green), var(--success-green-light));
  --gradient-error: linear-gradient(135deg, var(--error-red), var(--error-red-light));
  --gradient-warning: linear-gradient(135deg, var(--warning-orange), var(--warning-orange-light));

  /* Shadows */
  --shadow-sm: 0 1px 2px rgba(9, 30, 66, 0.08);
  --shadow-md: 0 2px 8px rgba(9, 30, 66, 0.12);
  --shadow-lg: 0 4px 16px rgba(9, 30, 66, 0.16);
  --shadow-xl: 0 8px 32px rgba(9, 30, 66, 0.20);

  /* Border Radius */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --radius-full: 50%;

  /* Typography */
  --font-family: 'Inter', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  --font-weight-light: 300;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* Transitions */
  --transition-fast: all 0.15s ease;
  --transition-normal: all 0.25s ease;
  --transition-slow: all 0.35s ease;
}

body {
  font-family: var(--font-family);
  background-color: var(--neutral-gray-50);
  color: var(--neutral-gray-900);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.App {
  min-height: 100vh;
  transition: var(--transition-normal);
}

.app-container {
  padding-top: 60px; /* Space for the navigation bar */
  min-height: calc(100vh - 60px);
  animation: fadeIn 0.5s ease;
}

.app-loading {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
  font-size: 1.2rem;
  color: var(--primary-blue);
  background-color: var(--neutral-white);
}

/* Loading Spinner */
.loading-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  background: var(--gradient-primary);
  color: var(--neutral-white);
  font-size: 1.25rem;
  font-weight: var(--font-weight-medium);
  position: relative;
}

.loading-spinner::before {
  content: '';
  width: 50px;
  height: 50px;
  border: 4px solid rgba(255, 255, 255, 0.2);
  border-top: 4px solid var(--neutral-white);
  border-radius: var(--radius-full);
  animation: spin 1s linear infinite;
  margin-right: 20px;
}

/* Utility Classes */
.text-center {
  text-align: center;
}

.text-primary {
  color: var(--primary-blue);
}

.text-success {
  color: var(--success-green);
}

.text-warning {
  color: var(--warning-orange);
}

.text-error {
  color: var(--error-red);
}

.bg-primary {
  background: var(--gradient-primary);
}

.bg-success {
  background: var(--gradient-success);
}

.bg-error {
  background: var(--gradient-error);
}

.bg-warning {
  background: var(--gradient-warning);
}

/* Button Styles */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 12px 24px;
  border: none;
  border-radius: var(--radius-md);
  font-family: inherit;
  font-size: 1rem;
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: var(--transition-normal);
  text-decoration: none;
  box-shadow: var(--shadow-sm);
  min-height: 44px;
}

.btn:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-md);
}

.btn:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

.btn-primary {
  background: var(--gradient-primary);
  color: var(--neutral-white);
}

.btn-primary:hover {
  background: linear-gradient(135deg, var(--primary-blue-dark), var(--primary-blue));
}

.btn-success {
  background: var(--gradient-success);
  color: var(--neutral-white);
}

.btn-success:hover {
  background: linear-gradient(135deg, var(--success-green-dark), var(--success-green));
}

.btn-error {
  background: var(--gradient-error);
  color: var(--neutral-white);
}

.btn-error:hover {
  background: linear-gradient(135deg, var(--error-red-dark), var(--error-red));
}

/* Card Styles */
.card {
  background: var(--neutral-white);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
  padding: 24px;
  transition: var(--transition-normal);
}

.card:hover {
  box-shadow: var(--shadow-lg);
  transform: translateY(-4px);
}

/* Status Indicators */
.status-indicator {
  display: inline-flex;
  align-items: center;
  padding: 6px 12px;
  border-radius: var(--radius-full);
  font-size: 0.875rem;
  font-weight: var(--font-weight-medium);
}

.status-success {
  background: rgba(54, 179, 126, 0.1);
  color: var(--success-green-dark);
  border: 1px solid rgba(54, 179, 126, 0.2);
}

.status-error {
  background: rgba(222, 53, 11, 0.1);
  color: var(--error-red-dark);
  border: 1px solid rgba(222, 53, 11, 0.2);
}

.status-warning {
  background: rgba(255, 171, 0, 0.1);
  color: var(--warning-orange-dark);
  border: 1px solid rgba(255, 171, 0, 0.2);
}

/* Animations */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(66, 153, 225, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(66, 153, 225, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(66, 153, 225, 0);
  }
}

/* Focus States */
*:focus {
  outline: none;
}

*:focus-visible {
  outline: 2px solid var(--primary-blue);
  outline-offset: 2px;
}

/* Scrollbar Styling */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--neutral-gray-100);
  border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb {
  background: var(--neutral-gray-300);
  border-radius: var(--radius-sm);
  transition: var(--transition-fast);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--neutral-gray-400);
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
  :root {
    --neutral-white: #1a1a1a;
    --neutral-gray-50: #111111;
    --neutral-gray-100: #1e1e1e;
    --neutral-gray-200: #2a2a2a;
    --neutral-gray-900: #ffffff;
    --neutral-gray-800: #e5e5e5;
    --neutral-gray-700: #cccccc;
  }
}

/* Responsive Design */
@media (max-width: 1200px) {
  .App {
    padding: 0 20px;
  }
}

@media (max-width: 768px) {
  .App {
    padding: 0 16px;
  }

  .btn {
    padding: 10px 20px;
    font-size: 0.9rem;
  }

  .card {
    padding: 20px;
  }
}

@media (max-width: 480px) {
  .App {
    padding: 0 12px;
  }

  .btn {
    padding: 8px 16px;
    font-size: 0.85rem;
    width: 100%;
  }

  .card {
    padding: 16px;
  }
}
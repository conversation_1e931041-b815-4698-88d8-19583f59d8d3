/* Enhanced Navigation.css - 10x Better with Admin Routes */
:root {
  /* Navigation Color Palette */
  --nav-primary: #2c3e50;
  --nav-primary-dark: #1a252f;
  --nav-primary-light: #34495e;
  --nav-secondary: #3498db;
  --nav-accent: #9b59b6;
  --nav-success: #2ecc71;
  --nav-warning: #f39c12;
  --nav-error: #e74c3c;
  --nav-error-dark: #c0392b;
  --nav-admin: #8e44ad;
  --nav-admin-light: #9c56c7;
  --nav-admin-dark: #7d3c98;
  --nav-online: #2ecc71;
  --nav-offline: #e74c3c;

  /* Gradients */
  --nav-gradient-primary: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
  --nav-gradient-header: linear-gradient(90deg, #2c3e50 0%, #3498db 100%);
  --nav-gradient-active: linear-gradient(135deg, rgba(255, 255, 255, 0.2) 0%, rgba(255, 255, 255, 0.1) 100%);
  --nav-gradient-error: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
  --nav-gradient-admin: linear-gradient(135deg, #8e44ad 0%, #9c56c7 100%);
  --nav-glass: rgba(255, 255, 255, 0.1);

  /* Neutral Colors */
  --nav-white: #ffffff;
  --nav-white-alpha-10: rgba(255, 255, 255, 0.1);
  --nav-white-alpha-20: rgba(255, 255, 255, 0.2);
  --nav-white-alpha-30: rgba(255, 255, 255, 0.3);
  --nav-black-alpha-10: rgba(0, 0, 0, 0.1);
  --nav-black-alpha-20: rgba(0, 0, 0, 0.2);

  /* Shadows */
  --nav-shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  --nav-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --nav-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --nav-shadow-glow: 0 0 20px rgba(52, 152, 219, 0.3);
  --nav-shadow-admin: 0 0 20px rgba(142, 68, 173, 0.3);

  /* Border Radius */
  --nav-radius-sm: 0.25rem;
  --nav-radius-md: 0.375rem;
  --nav-radius-lg: 0.5rem;
  --nav-radius-xl: 0.75rem;
  --nav-radius-2xl: 1rem;
  --nav-radius-full: 9999px;

  /* Transitions */
  --nav-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --nav-bounce: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --nav-elastic: all 0.4s cubic-bezier(0.68, -0.6, 0.32, 1.6);

  /* Z-Index */
  --nav-z-index: 1000;
}

* {
  box-sizing: border-box;
}

.navigation {
  background: var(--nav-primary);
  color: var(--nav-white);
  padding: 0.75rem 0;
  box-shadow: var(--nav-shadow-lg);
  position: sticky;
  top: 0;
  z-index: var(--nav-z-index);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--nav-white-alpha-10);
  animation: navSlideDown 0.6s ease-out;
}

@keyframes navSlideDown {
  from {
    opacity: 0;
    transform: translateY(-100%);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.nav-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 2rem;
  position: relative;
}

.nav-logo {
  display: flex;
  align-items: center;
  gap: 1rem;
  cursor: pointer;
  transition: var(--nav-transition);
  animation: logoSlideIn 0.8s ease-out 0.2s both;
}

@keyframes logoSlideIn {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.nav-logo:hover {
  transform: scale(1.05);
}

.nav-logo-img {
  height: 45px;
  width: auto;
  border-radius: var(--nav-radius-lg);
  box-shadow: var(--nav-shadow-md);
  transition: var(--nav-transition);
  background: var(--nav-white-alpha-10);
  padding: 0.5rem;
  backdrop-filter: blur(5px);
}

.nav-logo-img:hover {
  transform: rotate(5deg) scale(1.1);
  box-shadow: var(--nav-shadow-lg), var(--nav-shadow-glow);
}

.nav-logo-text {
  font-size: 1.5rem;
  font-weight: 900;
  background: linear-gradient(135deg, var(--nav-white) 0%, #bfdbfe 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  position: relative;
}

.nav-logo-text::after {
  content: '';
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 0;
  height: 2px;
  background: linear-gradient(90deg, var(--nav-secondary), var(--nav-accent));
  border-radius: var(--nav-radius-full);
  transition: width 0.5s ease;
}

.nav-logo:hover .nav-logo-text::after {
  width: 100%;
}

/* Connection status indicator */
.connection-status {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: var(--nav-radius-full);
  margin-left: 0.5rem;
  font-weight: 600;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  animation: fadeIn 0.5s ease-out;
}

.connection-status.online {
  background-color: rgba(46, 204, 113, 0.2);
  color: var(--nav-online);
  border: 1px solid var(--nav-online);
}

.connection-status.offline {
  background-color: rgba(231, 76, 60, 0.2);
  color: var(--nav-offline);
  border: 1px solid var(--nav-offline);
  animation: pulse 2s infinite;
}

.offline-indicator {
  background-color: var(--nav-offline);
  color: white;
  text-align: center;
  padding: 0.5rem;
  font-weight: 600;
  animation: slideDown 0.5s ease-out, pulse 2s infinite;
  position: relative;
  z-index: 1002;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

@keyframes slideDown {
  from { transform: translateY(-100%); }
  to { transform: translateY(0); }
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.nav-links {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: 0.5rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border-radius: var(--nav-radius-2xl);
  padding: 0.5rem;
  animation: linksSlideIn 0.8s ease-out 0.4s both;
}

@keyframes linksSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.nav-links li {
  margin: 0;
  position: relative;
}

.nav-links li a {
  color: var(--nav-white);
  text-decoration: none;
  padding: 0.75rem 1.5rem;
  border-radius: var(--nav-radius-xl);
  transition: var(--nav-bounce);
  font-weight: 600;
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.nav-links li a::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, var(--nav-white-alpha-20), transparent);
  transition: left 0.5s;
}

.nav-links li a:hover::before {
  left: 100%;
}

/* Regular active state */
.nav-links li.active a {
  background: var(--nav-gradient-active);
  box-shadow: var(--nav-shadow-sm);
  border: 1px solid var(--nav-white-alpha-30);
}

/* Admin route styling */
.nav-links li.admin-route a {
  background: rgba(142, 68, 173, 0.1);
  border: 1px solid rgba(142, 68, 173, 0.2);
  animation: adminRouteSlideIn 0.8s ease-out 0.8s both;
}

@keyframes adminRouteSlideIn {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.nav-links li.admin-route a:hover {
  background: rgba(142, 68, 173, 0.2);
  box-shadow: var(--nav-shadow-md), var(--nav-shadow-admin);
}

/* Admin active state */
.nav-links li.admin-route.active a {
  background: var(--nav-gradient-admin);
  box-shadow: var(--nav-shadow-sm), var(--nav-shadow-admin);
  border: 1px solid var(--nav-admin-light);
}

/* Admin badge indicator */
.nav-links li.admin-route a::after {
  content: '👑';
  position: absolute;
  top: -5px;
  right: -5px;
  font-size: 0.8rem;
  background: var(--nav-admin);
  padding: 2px 6px;
  border-radius: var(--nav-radius-full);
  border: 2px solid var(--nav-primary);
  animation: adminBadgePulse 2s ease-in-out infinite;
}

@keyframes adminBadgePulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.nav-links li a:hover {
  background: var(--nav-white-alpha-20);
  transform: translateY(-2px);
  box-shadow: var(--nav-shadow-md);
}

.nav-links li a:active {
  transform: translateY(0);
  transition: transform 0.1s;
}

/* Navigation Link Structure */
.nav-link-text {
  flex: 1;
}

.nav-link-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.1rem;
}

.logout-btn {
  background: var(--nav-gradient-error);
  color: var(--nav-white);
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: var(--nav-radius-xl);
  cursor: pointer;
  transition: var(--nav-bounce);
  font-weight: 700;
  font-size: 0.95rem;
  position: relative;
  overflow: hidden;
  box-shadow: var(--nav-shadow-md);
  animation: logoutSlideIn 0.8s ease-out 0.6s both;
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

@keyframes logoutSlideIn {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.logout-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, var(--nav-white-alpha-30), transparent);
  transition: left 0.5s;
}

.logout-btn:hover::before {
  left: 100%;
}

.logout-btn:hover {
  background: linear-gradient(135deg, var(--nav-error-dark), #a93226);
  transform: translateY(-2px) scale(1.05);
  box-shadow: var(--nav-shadow-lg), 0 0 20px rgba(231, 76, 60, 0.4);
}

.logout-btn:active {
  transform: translateY(0) scale(1.02);
  transition: transform 0.1s;
}

.nav-mobile-toggle {
  display: none;
  flex-direction: column;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: var(--nav-radius-lg);
  background: var(--nav-white-alpha-10);
  backdrop-filter: blur(5px);
  transition: var(--nav-transition);
  position: relative;
  z-index: 1001;
}

.nav-mobile-toggle:hover {
  background: var(--nav-white-alpha-20);
  transform: scale(1.1);
}

.nav-mobile-toggle span {
  display: block;
  width: 28px;
  height: 3px;
  background-color: var(--nav-white);
  margin: 3px 0;
  border-radius: var(--nav-radius-sm);
  transition: var(--nav-transition);
  transform-origin: center;
}

.nav-mobile-toggle.active span:nth-child(1) {
  transform: rotate(45deg) translate(6px, 6px);
}

.nav-mobile-toggle.active span:nth-child(2) {
  opacity: 0;
  transform: scale(0);
}

.nav-mobile-toggle.active span:nth-child(3) {
  transform: rotate(-45deg) translate(6px, -6px);
}

/* User Profile Section */
.nav-user-profile {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: var(--nav-white-alpha-10);
  backdrop-filter: blur(10px);
  padding: 0.5rem 1rem;
  border-radius: var(--nav-radius-xl);
  transition: var(--nav-transition);
}

.nav-user-profile:hover {
  background: var(--nav-white-alpha-20);
}

.nav-user-avatar {
  width: 35px;
  height: 35px;
  border-radius: 50%;
  background: var(--nav-secondary);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 1.1rem;
  box-shadow: var(--nav-shadow-sm);
}

.nav-user-name {
  font-weight: 600;
  font-size: 0.95rem;
}

/* Search Bar in Navigation */
.nav-search {
  position: relative;
  margin: 0 2rem;
}

.nav-search input {
  background: var(--nav-white-alpha-10);
  border: 1px solid var(--nav-white-alpha-20);
  border-radius: var(--nav-radius-xl);
  padding: 0.75rem 3rem 0.75rem 1rem;
  color: var(--nav-white);
  font-size: 0.95rem;
  width: 250px;
  backdrop-filter: blur(10px);
  transition: var(--nav-transition);
}

.nav-search input::placeholder {
  color: var(--nav-white-alpha-30);
}

.nav-search input:focus {
  outline: none;
  background: var(--nav-white-alpha-20);
  border-color: var(--nav-secondary);
  box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
  width: 300px;
}

.nav-search::after {
  content: '🔍';
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  font-size: 1.1rem;
  opacity: 0.7;
}

/* Responsive Design */
@media (max-width: 768px) {
  .nav-container {
    padding: 0 1rem;
  }

  .nav-mobile-toggle {
    display: flex;
  }

  .nav-links {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    flex-direction: column;
    background: var(--nav-gradient-primary);
    backdrop-filter: blur(20px);
    padding: 2rem;
    display: none;
    box-shadow: var(--nav-shadow-lg);
    gap: 0.5rem;
    border-top: 1px solid var(--nav-white-alpha-20);
    animation: mobileMenuSlide 0.4s ease-out;
  }

  @keyframes mobileMenuSlide {
    from {
      opacity: 0;
      transform: translateY(-20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .nav-links.active {
    display: flex;
  }

  .nav-links li {
    margin: 0;
    width: 100%;
  }

  .nav-links li a {
    display: flex;
    justify-content: space-between;
    padding: 1rem 1.5rem;
    width: 100%;
    border-radius: var(--nav-radius-lg);
  }

  /* Mobile admin badge positioning */
  .nav-links li.admin-route a::after {
    top: 5px;
    right: 10px;
  }

  .nav-logout {
    margin-top: 1rem;
    padding-top: 1rem;
    border-top: 1px solid var(--nav-white-alpha-20);
  }

  .logout-btn {
    width: 100%;
    justify-content: center;
    padding: 1rem 1.5rem;
  }

  .nav-search {
    display: none;
  }

  .nav-user-profile {
    display: none;
  }

  /* Responsive scroll to top button */
  .scroll-to-top {
    width: 45px;
    height: 45px;
    bottom: 20px;
    right: 20px;
    font-size: 1.2rem;
  }
}

@media (max-width: 480px) {
  .nav-logo-text {
    font-size: 1.2rem;
  }

  .nav-logo-img {
    height: 35px;
  }

  .nav-container {
    padding: 0 0.75rem;
  }

  .nav-links {
    padding: 1rem;
  }

  .nav-links li a {
    padding: 0.875rem 1rem;
    font-size: 0.9rem;
  }

  /* Even smaller scroll to top button for mobile */
  .scroll-to-top {
    width: 40px;
    height: 40px;
    bottom: 15px;
    right: 15px;
    font-size: 1rem;
  }
}

/* Dark Theme Adjustments */
@media (prefers-color-scheme: dark) {
  .navigation {
    background: linear-gradient(135deg, #1a1a1a 0%, #2c2c2c 100%);
    border-bottom-color: rgba(255, 255, 255, 0.05);
  }

  .nav-links {
    background: rgba(255, 255, 255, 0.05);
  }

  .nav-links li.active a {
    background: rgba(255, 255, 255, 0.15);
  }

  .nav-links li.admin-route a {
    background: rgba(142, 68, 173, 0.1);
  }

  .nav-links li.admin-route.active a {
    background: rgba(142, 68, 173, 0.25);
  }

  .logout-btn {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .navigation {
    border-bottom: 3px solid currentColor;
  }

  .nav-links li a,
  .logout-btn {
    border: 2px solid currentColor;
  }

  .nav-links li.active a {
    background: var(--nav-white);
    color: var(--nav-primary);
  }

  .nav-links li.admin-route a {
    border-color: var(--nav-admin);
  }

  .nav-links li.admin-route.active a {
    background: var(--nav-admin);
    color: var(--nav-white);
  }
}

/* Print Styles */
@media print {
  .navigation {
    display: none;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Focus States */
.nav-links li a:focus-visible,
.logout-btn:focus-visible,
.nav-mobile-toggle:focus-visible,
.scroll-to-top:focus-visible {
  outline: 3px solid var(--nav-secondary);
  outline-offset: 3px;
}

.nav-links li.admin-route a:focus-visible {
  outline-color: var(--nav-admin);
}

/* Scroll to Top Button */
.scroll-to-top {
  position: fixed;
  bottom: 30px;
  right: 30px;
  width: 50px;
  height: 50px;
  background: var(--nav-gradient-primary);
  color: var(--nav-white);
  border: none;
  border-radius: var(--nav-radius-full);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 1.5rem;
  box-shadow: var(--nav-shadow-lg);
  opacity: 0;
  visibility: hidden;
  transform: translateY(20px);
  transition: var(--nav-transition);
  z-index: 999;
}

.scroll-to-top.visible {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}

.scroll-to-top:hover {
  background: var(--nav-gradient-header);
  transform: translateY(-5px);
  box-shadow: var(--nav-shadow-lg), var(--nav-shadow-glow);
}

.scroll-to-top:active {
  transform: translateY(0);
}

.scroll-arrow {
  animation: bounce 2s infinite;
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-5px);
  }
  60% {
    transform: translateY(-3px);
  }
}

/* Notification Badge */
.nav-notification {
  position: relative;
}

.nav-notification::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 8px;
  height: 8px;
  background: var(--nav-error);
  border-radius: 50%;
  box-shadow: 0 0 0 2px var(--nav-primary);
  animation: notificationPulse 2s ease-in-out infinite;
}

@keyframes notificationPulse {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.7; transform: scale(1.1); }
}

/* Quick Actions */
.nav-quick-actions {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-left: 1rem;
}

.nav-quick-action {
  background: var(--nav-white-alpha-10);
  border: none;
  color: var(--nav-white);
  padding: 0.5rem;
  border-radius: var(--nav-radius-lg);
  cursor: pointer;
  transition: var(--nav-transition);
  font-size: 1.1rem;
  backdrop-filter: blur(5px);
}

.nav-quick-action:hover {
  background: var(--nav-white-alpha-20);
  transform: scale(1.1);
}

/* Loading State */
.navigation.loading {
  opacity: 0.8;
  pointer-events: none;
}

.navigation.loading::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  background: linear-gradient(90deg, var(--nav-secondary), var(--nav-accent));
  animation: loadingBar 1.5s ease-in-out infinite;
}

@keyframes loadingBar {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}
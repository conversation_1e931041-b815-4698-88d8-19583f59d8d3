/* Enhanced Profile.css - 10x Better */
:root {
  /* Profile Color Palette */
  --profile-primary: #3b82f6;
  --profile-primary-dark: #1d4ed8;
  --profile-primary-light: #60a5fa;
  --profile-secondary: #8b5cf6;
  --profile-accent: #06b6d4;
  --profile-success: #10b981;
  --profile-warning: #f59e0b;
  --profile-error: #ef4444;
  --profile-info: #3b82f6;
  
  /* Role Colors */
  --role-admin: #dc2626;
  --role-admin-bg: rgba(220, 38, 38, 0.1);
  --role-employee: #2563eb;
  --role-employee-bg: rgba(37, 99, 235, 0.1);
  --role-manager: #9333ea;
  --role-manager-bg: rgba(147, 51, 234, 0.1);
  
  /* Gradients */
  --profile-gradient-primary: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
  --profile-gradient-card: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
  --profile-gradient-header: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
  --profile-gradient-success: linear-gradient(135deg, #10b981 0%, #34d399 100%);
  --profile-gradient-error: linear-gradient(135deg, #ef4444 0%, #f87171 100%);
  --profile-glass: rgba(255, 255, 255, 0.9);
  
  /* Neutral Colors */
  --profile-white: #ffffff;
  --profile-gray-50: #f8fafc;
  --profile-gray-100: #f1f5f9;
  --profile-gray-200: #e2e8f0;
  --profile-gray-300: #cbd5e1;
  --profile-gray-400: #94a3b8;
  --profile-gray-500: #64748b;
  --profile-gray-600: #475569;
  --profile-gray-700: #334155;
  --profile-gray-800: #1e293b;
  --profile-gray-900: #0f172a;
  
  /* Shadows */
  --profile-shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.05);
  --profile-shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  --profile-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --profile-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --profile-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --profile-shadow-glow: 0 0 40px rgba(59, 130, 246, 0.3);
  
  /* Border Radius */
  --profile-radius-sm: 0.375rem;
  --profile-radius-md: 0.5rem;
  --profile-radius-lg: 0.75rem;
  --profile-radius-xl: 1rem;
  --profile-radius-2xl: 1.5rem;
  --profile-radius-3xl: 2rem;
  --profile-radius-full: 9999px;
  
  /* Transitions */
  --profile-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --profile-bounce: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --profile-elastic: all 0.4s cubic-bezier(0.68, -0.6, 0.32, 1.6);
}

* {
  box-sizing: border-box;
}

body {
  font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, sans-serif;
  background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  color: var(--profile-gray-900);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  min-height: 100vh;
  padding: 2rem 0;
}

.profile-container {
  max-width: 900px;
  margin: 2rem auto;
  padding: 0 1.5rem;
  animation: containerSlideIn 0.8s ease-out;
  position: relative;
}

@keyframes containerSlideIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.profile-container::before {
  content: '';
  position: absolute;
  top: -50px;
  left: -50px;
  right: -50px;
  bottom: -50px;
  background: 
    radial-gradient(circle at 25% 25%, rgba(59, 130, 246, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(139, 92, 246, 0.05) 0%, transparent 50%);
  animation: backgroundShift 15s ease-in-out infinite;
  z-index: 0;
}

@keyframes backgroundShift {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.profile-container > * {
  position: relative;
  z-index: 1;
}

.profile-card {
  background: var(--profile-glass);
  backdrop-filter: blur(20px) saturate(150%);
  border-radius: var(--profile-radius-3xl);
  box-shadow: var(--profile-shadow-xl);
  overflow: hidden;
  border: 1px solid rgba(255, 255, 255, 0.3);
  animation: cardSlideUp 0.8s ease-out 0.2s both;
  position: relative;
}

@keyframes cardSlideUp {
  from {
    opacity: 0;
    transform: translateY(50px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.profile-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--profile-gradient-primary);
  border-radius: var(--profile-radius-3xl) var(--profile-radius-3xl) 0 0;
}

.profile-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 2rem 2.5rem;
  background: var(--profile-gradient-header);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(226, 232, 240, 0.6);
  position: relative;
}

.profile-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent, var(--profile-primary), transparent);
}

.profile-header h2 {
  margin: 0;
  color: var(--profile-gray-800);
  font-size: 2rem;
  font-weight: 800;
  background: var(--profile-gradient-primary);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  position: relative;
  animation: titleSlide 0.8s ease-out 0.4s both;
}

@keyframes titleSlide {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.profile-header h2::after {
  content: '👤';
  position: absolute;
  right: -2.5rem;
  top: 50%;
  transform: translateY(-50%);
  font-size: 1.5rem;
  opacity: 0.7;
  animation: iconFloat 3s ease-in-out infinite;
}

@keyframes iconFloat {
  0%, 100% { transform: translateY(-50%); }
  50% { transform: translateY(-70%); }
}

.profile-edit-btn {
  background: var(--profile-gradient-primary);
  color: var(--profile-white);
  border: none;
  padding: 0.875rem 1.75rem;
  border-radius: var(--profile-radius-xl);
  cursor: pointer;
  transition: var(--profile-bounce);
  font-weight: 600;
  font-size: 1rem;
  box-shadow: var(--profile-shadow-md);
  position: relative;
  overflow: hidden;
  animation: buttonSlide 0.8s ease-out 0.6s both;
}

@keyframes buttonSlide {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.profile-edit-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.profile-edit-btn:hover::before {
  left: 100%;
}

.profile-edit-btn:hover {
  background: linear-gradient(135deg, var(--profile-primary-dark), var(--profile-secondary));
  transform: translateY(-3px) scale(1.05);
  box-shadow: var(--profile-shadow-lg), var(--profile-shadow-glow);
}

.profile-edit-btn:active {
  transform: translateY(-1px) scale(1.02);
}

.profile-edit-btn.cancel {
  background: linear-gradient(135deg, var(--profile-gray-500), var(--profile-gray-600));
  color: var(--profile-white);
}

.profile-edit-btn.cancel:hover {
  background: linear-gradient(135deg, var(--profile-gray-600), var(--profile-gray-700));
}

.profile-edit-btn::after {
  content: var(--btn-icon, '✏️');
  margin-left: 0.5rem;
  font-size: 1rem;
}

.profile-edit-btn.cancel::after {
  --btn-icon: '❌';
}

.profile-error {
  background: linear-gradient(135deg, rgba(239, 68, 68, 0.1) 0%, rgba(248, 113, 113, 0.05) 100%);
  color: var(--profile-gray-800);
  padding: 1.25rem 1.5rem;
  margin: 1.5rem;
  border-radius: var(--profile-radius-xl);
  border: 2px solid rgba(239, 68, 68, 0.2);
  border-left: 4px solid var(--profile-error);
  box-shadow: var(--profile-shadow-md);
  display: flex;
  align-items: center;
  gap: 1rem;
  font-weight: 600;
  animation: errorSlideIn 0.5s ease-out, errorShake 0.6s ease-in-out 0.5s;
}

@keyframes errorSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes errorShake {
  0%, 20%, 40%, 60%, 80% { transform: translateX(0); }
  10%, 30%, 50%, 70%, 90% { transform: translateX(-3px); }
  15%, 35%, 55%, 75% { transform: translateX(3px); }
}

.profile-error::before {
  content: '⚠️';
  font-size: 1.5rem;
  animation: errorPulse 1.5s ease-in-out infinite;
}

@keyframes errorPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.profile-content {
  display: flex;
  padding: 2.5rem;
  gap: 2.5rem;
  animation: contentSlideIn 0.8s ease-out 0.4s both;
}

@keyframes contentSlideIn {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.profile-avatar {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1.5rem;
}

.avatar-circle {
  width: 120px;
  height: 120px;
  background: var(--profile-gradient-primary);
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;
  color: var(--profile-white);
  font-size: 3rem;
  font-weight: 900;
  box-shadow: var(--profile-shadow-xl);
  position: relative;
  transition: var(--profile-transition);
  cursor: pointer;
  animation: avatarFloat 0.8s ease-out 0.5s both;
}

@keyframes avatarFloat {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(20px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

.avatar-circle::before {
  content: '';
  position: absolute;
  inset: -4px;
  background: conic-gradient(from 0deg, var(--profile-primary), var(--profile-secondary), var(--profile-accent), var(--profile-primary));
  border-radius: 50%;
  z-index: -1;
  opacity: 0.7;
  animation: avatarGlow 3s linear infinite;
}

@keyframes avatarGlow {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.avatar-circle:hover {
  transform: scale(1.05);
  box-shadow: var(--profile-shadow-2xl), 0 0 40px rgba(59, 130, 246, 0.4);
}

.profile-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.profile-field {
  position: relative;
  animation: fieldSlideIn 0.6s ease-out;
  animation-fill-mode: both;
}

.profile-field:nth-child(1) { animation-delay: 0.1s; }
.profile-field:nth-child(2) { animation-delay: 0.2s; }
.profile-field:nth-child(3) { animation-delay: 0.3s; }
.profile-field:nth-child(4) { animation-delay: 0.4s; }

@keyframes fieldSlideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.profile-field label {
  display: block;
  color: var(--profile-gray-600);
  margin-bottom: 0.75rem;
  font-size: 0.875rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.profile-field label::before {
  content: var(--field-icon);
  font-size: 1rem;
}

.profile-field:nth-child(1) label::before { --field-icon: '👤'; }
.profile-field:nth-child(2) label::before { --field-icon: '📧'; }
.profile-field:nth-child(3) label::before { --field-icon: '📱'; }
.profile-field:nth-child(4) label::before { --field-icon: '🏢'; }

.profile-field span {
  font-size: 1.125rem;
  color: var(--profile-gray-800);
  font-weight: 500;
  display: block;
  padding: 1rem 1.25rem;
  background: rgba(248, 250, 252, 0.8);
  border-radius: var(--profile-radius-lg);
  border: 2px solid transparent;
  transition: var(--profile-transition);
}

.profile-field span:hover {
  background: rgba(248, 250, 252, 1);
  border-color: var(--profile-primary-light);
}

.profile-input {
  width: 100%;
  padding: 1rem 1.25rem;
  border: 2px solid var(--profile-gray-200);
  border-radius: var(--profile-radius-lg);
  font-size: 1.125rem;
  font-family: inherit;
  background: var(--profile-white);
  color: var(--profile-gray-800);
  transition: var(--profile-transition);
  box-shadow: var(--profile-shadow-sm);
}

.profile-input:focus {
  outline: none;
  border-color: var(--profile-primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1), var(--profile-shadow-md);
  transform: translateY(-2px);
}

.profile-input:hover {
  border-color: var(--profile-primary-light);
}

.profile-role {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.25rem;
  border-radius: var(--profile-radius-full);
  font-size: 0.875rem;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  position: relative;
  transition: var(--profile-transition);
}

.profile-role::before {
  content: var(--role-icon);
  font-size: 1rem;
}

.profile-role.admin {
  background: var(--role-admin-bg);
  color: var(--role-admin);
  border: 2px solid rgba(220, 38, 38, 0.3);
  --role-icon: '👑';
}

.profile-role.employee {
  background: var(--role-employee-bg);
  color: var(--role-employee);
  border: 2px solid rgba(37, 99, 235, 0.3);
  --role-icon: '👨‍💼';
}

.profile-role.manager {
  background: var(--role-manager-bg);
  color: var(--role-manager);
  border: 2px solid rgba(147, 51, 234, 0.3);
  --role-icon: '👨‍💼';
}

.profile-role:hover {
  transform: scale(1.05);
  box-shadow: var(--profile-shadow-md);
}

.profile-save-btn {
  background: var(--profile-gradient-success);
  color: var(--profile-white);
  border: none;
  padding: 1rem 2rem;
  border-radius: var(--profile-radius-xl);
  cursor: pointer;
  transition: var(--profile-bounce);
  margin-top: 1.5rem;
  width: 100%;
  font-size: 1.125rem;
  font-weight: 700;
  box-shadow: var(--profile-shadow-lg);
  position: relative;
  overflow: hidden;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.profile-save-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.profile-save-btn:hover::before {
  left: 100%;
}

.profile-save-btn:hover {
  background: linear-gradient(135deg, #059669, #10b981);
  transform: translateY(-3px) scale(1.02);
  box-shadow: var(--profile-shadow-xl), 0 0 30px rgba(16, 185, 129, 0.4);
}

.profile-save-btn:active {
  transform: translateY(-1px) scale(1.01);
}

.profile-save-btn::after {
  content: '💾';
  margin-left: 0.75rem;
  font-size: 1.25rem;
}

.profile-reset-password {
  background: transparent;
  color: var(--profile-primary);
  border: none;
  padding: 0.75rem 0;
  cursor: pointer;
  text-decoration: none;
  margin-top: 1.5rem;
  transition: var(--profile-transition);
  font-weight: 600;
  font-size: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  justify-content: center;
  border-radius: var(--profile-radius-lg);
}

.profile-reset-password:hover {
  color: var(--profile-primary-dark);
  background: rgba(59, 130, 246, 0.05);
  transform: translateY(-2px);
}

.profile-reset-password::before {
  content: '🔐';
  font-size: 1.1rem;
}

.profile-loading {
  text-align: center;
  padding: 3rem;
  color: var(--profile-gray-600);
  position: relative;
  animation: loadingPulse 1.5s ease-in-out infinite;
}

@keyframes loadingPulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.profile-loading::before {
  content: '';
  display: inline-block;
  width: 40px;
  height: 40px;
  border: 4px solid var(--profile-gray-200);
  border-top: 4px solid var(--profile-primary);
  border-radius: 50%;
  animation: loadingSpinner 1s linear infinite;
  margin-bottom: 1.5rem;
}

@keyframes loadingSpinner {
  to { transform: rotate(360deg); }
}

.profile-loading p {
  font-size: 1.125rem;
  font-weight: 600;
  margin: 0;
}

/* Responsive Design */
@media (max-width: 768px) {
  .profile-container {
    padding: 0 1rem;
  }
  
  .profile-content {
    flex-direction: column;
    gap: 2rem;
    padding: 2rem;
  }
  
  .profile-avatar {
    align-self: center;
  }
  
  .avatar-circle {
    width: 100px;
    height: 100px;
    font-size: 2.5rem;
  }
  
  .profile-header {
    padding: 1.5rem;
    flex-direction: column;
    gap: 1rem;
    text-align: center;
  }
  
  .profile-header h2 {
    font-size: 1.75rem;
  }
  
  .profile-edit-btn {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .profile-content {
    padding: 1rem;
  }
  
  .profile-header {
    padding: 1rem;
  }
  
  .profile-header h2 {
    font-size: 1.5rem;
  }
  
  .profile-field {
    gap: 1.5rem;
  }
  
  .profile-input,
  .profile-field span {
    padding: 0.875rem 1rem;
    font-size: 1rem;
  }
}

/* Dark Theme Support */
@media (prefers-color-scheme: dark) {
  body {
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
    color: var(--profile-gray-100);
  }
  
  .profile-card {
    background: rgba(15, 23, 42, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }
  
  .profile-header {
    background: rgba(30, 41, 59, 0.8);
    border-bottom-color: rgba(255, 255, 255, 0.1);
  }
  
  .profile-field span {
    background: rgba(30, 41, 59, 0.6);
    color: var(--profile-gray-200);
  }
  
  .profile-input {
    background: rgba(30, 41, 59, 0.8);
    color: var(--profile-gray-100);
    border-color: rgba(255, 255, 255, 0.1);
  }
  
  .profile-field label {
    color: var(--profile-gray-400);
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .profile-card,
  .profile-input,
  .profile-save-btn,
  .profile-edit-btn {
    border: 2px solid currentColor;
  }
  
  .profile-error {
    border: 3px solid currentColor;
  }
}

/* Print Styles */
@media print {
  body {
    background: white;
    color: black;
  }
  
  .profile-container::before {
    display: none;
  }
  
  .profile-card {
    background: white;
    box-shadow: none;
    border: 1px solid #ccc;
  }
  
  .profile-edit-btn,
  .profile-save-btn,
  .profile-reset-password {
    display: none;
  }
  
  .avatar-circle::before {
    display: none;
  }
}

/* Accessibility Enhancements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  
  .profile-container::before,
  .avatar-circle::before {
    animation: none;
  }
}

/* Focus States */
.profile-input:focus-visible,
.profile-edit-btn:focus-visible,
.profile-save-btn:focus-visible,
.profile-reset-password:focus-visible {
  outline: 3px solid var(--profile-primary);
  outline-offset: 3px;
}
@echo off
echo 🚀 Démarrage du backend avec clé API Gemini...
echo.

REM Activer l'environnement tf_env
call venv\Scripts\activate.bat

REM Définir la clé API Gemini directement
set GEMINI_API_KEY=AIzaSyAaL0jybRUYfoy4NQDO6MhxIle4si3rtpI

echo ✅ Clé API Gemini définie
echo 📊 Serveur disponible sur: http://localhost:5000
echo 🌐 Interface React sur: http://localhost:3001/GeminiWebcam
echo.
echo Appuyez sur Ctrl+C pour arrêter le serveur
echo.

python app_clean.py

pause
a
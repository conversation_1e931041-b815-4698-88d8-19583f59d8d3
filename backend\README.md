# Système de Classification de Câbles

Ce projet implémente un système de classification de câbles basé sur l'apprentissage profond, capable de distinguer trois types de câbles :
- **cable normal** : Câble en bon état (affiché en vert)
- **cable redresse** : C<PERSON>ble redressé (affiché en orange)
- **cable defeaut** : Câble avec défaut (affiché en rouge)

## Caractéristiques principales

- Classification en temps réel via webcam ou caméra de téléphone Android
- Modèle de deep learning basé sur MobileNetV2 avec une précision de 97%
- Interface visuelle avec affichage des probabilités et code couleur
- Support pour différentes résolutions d'image
- Possibilité de capturer des images pendant l'analyse

## Modèle utilisé

Le système utilise un modèle Keras (`cable_classifier_final.keras`) qui a été entraîné sur un jeu de données augmenté pour atteindre une précision de 97%. Les performances par classe sont les suivantes :

| Classe | Précision | Rappel | F1-Score |
|--------|-----------|--------|----------|
| Câble normal | 97% | 99% | 98% |
| Câble redressé | 100% | 95% | 97% |
| Câble défectueux | 95% | 96% | 95% |

## Structure du projet

```
backend/
├── app.py                      # Application Flask principale
├── keras_model.py              # Fonctions pour le modèle Keras
├── detection_model.py          # Détecteur de câbles
├── cable_classifier_final.keras # Modèle Keras entraîné
├── uploads/                    # Dossier pour les images téléchargées
└── models/                     # Dossier pour les modèles (historique)
```

## Installation des dépendances

```bash
pip install -r requirements.txt
```

Ou installez les dépendances individuellement:

```bash
pip install flask flask-cors pillow tensorflow torch torchvision ultralytics opencv-python numpy
```

## Démarrage du serveur

```bash
cd backend
python app.py
```

Le serveur démarrera sur `http://localhost:5000`.

## API Endpoints

### Classification d'image

**Endpoint**: `/api/classify`
**Méthode**: POST
**Paramètres**:
- `image`: Fichier image à classifier

**Réponse**:
```json
{
  "predictions": [
    {"class": "cable defeaut", "probability": 0.05},
    {"class": "cable normal", "probability": 0.9},
    {"class": "cable redresse", "probability": 0.05}
  ],
  "top_prediction": {
    "class": "cable normal",
    "probability": 0.9
  },
  "metadata": {
    "filename": "20230501-123456_image.jpg",
    "model_name": "keras_classifier",
    "inference_time": 0.123,
    "timestamp": "20230501-123456",
    "model_type": "keras",
    "image_size": "640x480"
  }
}
```

### Détection et Classification

**Endpoint**: `/api/detect_and_classify`
**Méthode**: POST
**Paramètres**:
- `image`: Fichier image à analyser

**Réponse**:
```json
{
  "results": [
    {
      "bbox": [10, 20, 100, 200],
      "detection_confidence": 0.95,
      "detection_class": "cable",
      "classification": {
        "predictions": [...],
        "top_prediction": {...}
      }
    }
  ],
  "annotated_image": "data:image/jpeg;base64,...",
  "num_detections": 1,
  "best_result": {...},
  "detection_strategy": "advanced_cable_detection",
  "detection_version": "1.2",
  "message": "Détection améliorée avec analyse par modèle Keras"
}
```

### Liste des modèles disponibles

**Endpoint**: `/api/models`
**Méthode**: GET

**Réponse**:
```json
{
  "models": [
    {
      "name": "keras_classifier",
      "type": "keras",
      "description": "Fine-tuned Keras model for cable classification",
      "status": "loaded",
      "size": 42.5
    }
  ],
  "default_model": "keras_classifier"
}
```

### Vérification de l'état du serveur

**Endpoint**: `/api/health`
**Méthode**: GET

**Réponse**:
```json
{
  "status": "ok",
  "keras_model_loaded": true,
  "cable_detector_loaded": true,
  "timestamp": "2023-05-01T12:34:56.789Z"
}
```

## Codes couleur pour les classes

- **cable normal** : Vert (succès)
- **cable redresse** : Orange (avertissement)
- **cable defeaut** : Rouge (erreur)

## Développement

Pour ajouter de nouvelles fonctionnalités ou modifier le comportement existant, consultez les fichiers suivants:

- `app.py` : Points d'entrée de l'API et logique de routage
- `keras_model.py` : Fonctions pour charger et utiliser le modèle Keras
- `detection_model.py` : Détection des câbles dans les images

## Dépannage

Si vous rencontrez des problèmes avec le modèle Keras, vérifiez les points suivants:

1. Assurez-vous que le fichier `cable_classifier_final.keras` est présent dans le dossier `backend/`
2. Vérifiez que TensorFlow est correctement installé
3. Consultez les logs du serveur pour plus d'informations sur les erreurs

## Outils supplémentaires

### Test avec webcam

Pour tester le modèle en temps réel avec votre webcam:

```bash
python webcam_classifier.py --model cable_classifier_final.keras
```

### Utilisation de la caméra d'un téléphone Android

Pour améliorer la qualité d'image, vous pouvez utiliser la caméra de votre téléphone Android au lieu de la webcam de votre PC.

#### Option 1 : DroidCam (Recommandée)

1. **Installation** :
   - Sur votre téléphone Android : Installez [DroidCam](https://play.google.com/store/apps/details?id=com.dev47apps.droidcam)
   - Sur votre PC : Installez le [client DroidCam](https://www.dev47apps.com/)

2. **Configuration** :
   - Ouvrez l'application DroidCam sur votre téléphone
   - Notez l'adresse IP et le port affichés
   - Connectez-vous via le client DroidCam sur votre PC

3. **Utilisation** :
   ```bash
   python webcam_classifier.py --camera 1 --model cable_classifier_final.keras --resolution 720p
   ```

#### Option 2 : IP Webcam

1. **Installation** :
   - Sur votre téléphone Android : Installez [IP Webcam](https://play.google.com/store/apps/details?id=com.pas.webcam)

2. **Configuration** :
   - Ouvrez l'application et démarrez le serveur
   - Notez l'URL affichée (par exemple http://*************:8080)

3. **Utilisation** :
   ```bash
   python webcam_classifier.py --url "http://*************:8080/video" --model cable_classifier_final.keras
   ```

Pour plus de détails, consultez le fichier `ANDROID_CAMERA_GUIDE.md`.

### Optimisation du modèle

Pour optimiser le modèle pour le déploiement:

```bash
python optimize_model.py --benchmark --compare
```

Cela créera une version optimisée du modèle au format TensorFlow Lite et SavedModel.

## Conseils pour de meilleurs résultats

1. **Éclairage** : Assurez-vous que les câbles sont bien éclairés, évitez les ombres fortes et les reflets.

2. **Stabilité** : Utilisez un support ou un trépied pour maintenir la caméra stable.

3. **Distance et angle** : Maintenez une distance constante entre la caméra et les câbles, et trouvez l'angle optimal.

4. **Résolution** : Utilisez une résolution plus élevée pour plus de détails (720p ou 1080p).

/* Enhanced AdminDashboard.css - 10x Better Version 2 */
:root {
  /* Color Palette */
  --dashboard-primary: #0ea5e9;
  --dashboard-primary-dark: #0284c7;
  --dashboard-primary-light: #38bdf8;
  --dashboard-secondary: #6366f1;
  --dashboard-accent: #10b981;
  --dashboard-warning: #f59e0b;
  --dashboard-error: #ef4444;
  --dashboard-success: #22c55e;
  --dashboard-info: #3b82f6;

  /* Gradients */
  --dashboard-gradient-primary: linear-gradient(135deg, #0ea5e9 0%, #6366f1 100%);
  --dashboard-gradient-surface: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
  --dashboard-gradient-sidebar: linear-gradient(180deg, #1e293b 0%, #0f172a 100%);
  --dashboard-gradient-card: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.6) 100%);
  --dashboard-glass: rgba(255, 255, 255, 0.8);

  /* Neutral Colors */
  --dashboard-white: #ffffff;
  --dashboard-gray-50: #f8fafc;
  --dashboard-gray-100: #f1f5f9;
  --dashboard-gray-200: #e2e8f0;
  --dashboard-gray-300: #cbd5e1;
  --dashboard-gray-400: #94a3b8;
  --dashboard-gray-500: #64748b;
  --dashboard-gray-600: #475569;
  --dashboard-gray-700: #334155;
  --dashboard-gray-800: #1e293b;
  --dashboard-gray-900: #0f172a;

  /* Shadows */
  --dashboard-shadow-xs: 0 1px 2px rgba(0, 0, 0, 0.05);
  --dashboard-shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  --dashboard-shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --dashboard-shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --dashboard-shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  --dashboard-shadow-glow: 0 0 40px rgba(14, 165, 233, 0.3);

  /* Border Radius */
  --dashboard-radius-sm: 0.375rem;
  --dashboard-radius-md: 0.5rem;
  --dashboard-radius-lg: 0.75rem;
  --dashboard-radius-xl: 1rem;
  --dashboard-radius-2xl: 1.5rem;
  --dashboard-radius-3xl: 2rem;
  --dashboard-radius-full: 9999px;

  /* Transitions */
  --dashboard-transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  --dashboard-bounce: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  --dashboard-elastic: all 0.4s cubic-bezier(0.68, -0.6, 0.32, 1.6);
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, sans-serif;
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.dashboard-container {
  display: flex;
  min-height: 100vh;
  background: var(--dashboard-gradient-surface);
  position: relative;
  overflow-x: hidden;
}

.dashboard-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 25% 25%, rgba(14, 165, 233, 0.05) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(99, 102, 241, 0.05) 0%, transparent 50%);
  animation: backgroundShift 20s ease-in-out infinite;
  z-index: 0;
}

@keyframes backgroundShift {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.8; }
}

.dashboard-container > * {
  position: relative;
  z-index: 1;
}

/* Enhanced Sidebar */
.sidebar {
  background: var(--dashboard-gradient-sidebar);
  color: var(--dashboard-white);
  width: 280px;
  padding: 2rem;
  display: flex;
  flex-direction: column;
  box-shadow: var(--dashboard-shadow-xl);
  backdrop-filter: blur(20px);
  position: relative;
  overflow: hidden;
  animation: sidebarSlideIn 0.8s ease-out;
}

@keyframes sidebarSlideIn {
  from {
    opacity: 0;
    transform: translateX(-100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.sidebar::before {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 4px;
  height: 100%;
  background: var(--dashboard-gradient-primary);
  border-radius: 2px 0 0 2px;
}

.sidebar::after {
  content: '';
  position: absolute;
  top: -50%;
  right: -50%;
  width: 100%;
  height: 200%;
  background: radial-gradient(circle, rgba(14, 165, 233, 0.1) 0%, transparent 70%);
  animation: sidebarGlow 8s ease-in-out infinite;
}

@keyframes sidebarGlow {
  0%, 100% { opacity: 0.3; }
  50% { opacity: 0.7; }
}

.sidebar .logo {
  font-size: 1.875rem;
  font-weight: 900;
  background: linear-gradient(135deg, var(--dashboard-white) 0%, #bfdbfe 100%);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  text-align: center;
  margin-bottom: 3rem;
  padding: 1.5rem;
  border-radius: var(--dashboard-radius-xl);
  background-color: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  overflow: hidden;
  animation: logoFloat 3s ease-in-out infinite;
}

@keyframes logoFloat {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-5px); }
}

.sidebar .logo::before {
  content: '📊';
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  font-size: 1.5rem;
  animation: logoIconRotate 4s linear infinite;
}

@keyframes logoIconRotate {
  from { transform: translateY(-50%) rotate(0deg); }
  to { transform: translateY(-50%) rotate(360deg); }
}

.sidebar nav ul {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.sidebar nav ul li {
  margin-bottom: 0;
}

.sidebar nav ul li a,
.sidebar nav ul li button {
  color: #cbd5e1;
  text-decoration: none;
  display: flex;
  align-items: center;
  padding: 1rem 1.5rem;
  border-radius: var(--dashboard-radius-lg);
  transition: var(--dashboard-bounce);
  font-weight: 500;
  position: relative;
  overflow: hidden;
  backdrop-filter: blur(5px);
  background: transparent;
  border: none;
  width: 100%;
  text-align: left;
  cursor: pointer;
  font-family: inherit;
  font-size: 1rem;
}

.sidebar nav ul li a::before,
.sidebar nav ul li button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
  transition: left 0.5s;
}

.sidebar nav ul li a:hover::before,
.sidebar nav ul li button:hover::before {
  left: 100%;
}

.sidebar nav ul li a::after,
.sidebar nav ul li button::after {
  content: var(--nav-icon, '→');
  margin-left: auto;
  font-size: 1rem;
  opacity: 0;
  transform: translateX(-10px);
  transition: var(--dashboard-transition);
}

.sidebar nav ul li:nth-child(1) a,
.sidebar nav ul li:nth-child(1) button { --nav-icon: '📈'; }
.sidebar nav ul li:nth-child(2) a,
.sidebar nav ul li:nth-child(2) button { --nav-icon: '📊'; }
.sidebar nav ul li:nth-child(3) a,
.sidebar nav ul li:nth-child(3) button { --nav-icon: '🔍'; }

.sidebar nav ul li a:hover,
.sidebar nav ul li button:hover {
  background: rgba(255, 255, 255, 0.15);
  color: var(--dashboard-white);
  transform: translateX(8px);
  box-shadow: var(--dashboard-shadow-md);
}

.sidebar nav ul li a:hover::after,
.sidebar nav ul li button:hover::after {
  opacity: 1;
  transform: translateX(0);
}

.sidebar nav ul li a.active,
.sidebar nav ul li button.active {
  background: var(--dashboard-gradient-primary);
  color: var(--dashboard-white);
  transform: translateX(8px);
  box-shadow: var(--dashboard-shadow-lg);
}

.sidebar nav ul li a.active::after,
.sidebar nav ul li button.active::after {
  opacity: 1;
  transform: translateX(0);
}

/* Enhanced Main Content */
.main-content {
  flex-grow: 1;
  padding: 2.5rem;
  background: transparent;
  animation: contentSlideIn 0.8s ease-out 0.2s both;
}

@keyframes contentSlideIn {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.main-content header {
  margin-bottom: 3rem;
  padding-bottom: 2rem;
  border-bottom: 2px solid rgba(226, 232, 240, 0.6);
  background: var(--dashboard-glass);
  backdrop-filter: blur(15px);
  border-radius: var(--dashboard-radius-2xl);
  padding: 2.5rem;
  box-shadow: var(--dashboard-shadow-lg);
  animation: headerFloat 4s ease-in-out infinite;
  position: relative;
  overflow: hidden;
}

@keyframes headerFloat {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-5px); }
}

.main-content header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--dashboard-gradient-primary);
  border-radius: var(--dashboard-radius-2xl) var(--dashboard-radius-2xl) 0 0;
}

.main-content header h1 {
  font-size: clamp(2rem, 5vw, 3.5rem);
  font-weight: 900;
  background: var(--dashboard-gradient-primary);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin: 0;
  position: relative;
  display: inline-block;
  animation: titleSlide 1s ease-out 0.5s both;
}

@keyframes titleSlide {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.main-content header h1::after {
  content: '';
  position: absolute;
  bottom: -8px;
  left: 0;
  width: 0;
  height: 4px;
  background: var(--dashboard-gradient-primary);
  border-radius: var(--dashboard-radius-sm);
  animation: underlineGrow 1s ease-out 1s both;
}

@keyframes underlineGrow {
  from { width: 0; }
  to { width: 100%; }
}

/* Enhanced Widgets */
.widgets-container {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 2rem;
  margin-bottom: 3rem;
  animation: widgetsSlideUp 0.8s ease-out 0.4s both;
}

@keyframes widgetsSlideUp {
  from {
    opacity: 0;
    transform: translateY(50px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.widget {
  background: var(--dashboard-gradient-card);
  backdrop-filter: blur(15px);
  padding: 2rem;
  border-radius: var(--dashboard-radius-2xl);
  box-shadow: var(--dashboard-shadow-lg);
  border: 1px solid rgba(255, 255, 255, 0.3);
  transition: var(--dashboard-transition);
  position: relative;
  overflow: hidden;
  animation: widgetFloat 0.8s ease-out;
  animation-fill-mode: both;
}

.widget:nth-child(1) { animation-delay: 0.1s; }
.widget:nth-child(2) { animation-delay: 0.2s; }
.widget:nth-child(3) { animation-delay: 0.3s; }
.widget:nth-child(4) { animation-delay: 0.4s; }

@keyframes widgetFloat {
  from {
    opacity: 0;
    transform: translateY(30px) rotateX(10deg);
  }
  to {
    opacity: 1;
    transform: translateY(0) rotateX(0);
  }
}

.widget::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: var(--widget-gradient, var(--dashboard-gradient-primary));
  border-radius: var(--dashboard-radius-2xl) var(--dashboard-radius-2xl) 0 0;
}

.widget:nth-child(1) { --widget-gradient: linear-gradient(90deg, #f59e0b, #fbbf24); }
.widget:nth-child(2) { --widget-gradient: linear-gradient(90deg, #ef4444, #f87171); }
.widget:nth-child(3) { --widget-gradient: linear-gradient(90deg, #22c55e, #4ade80); }
.widget:nth-child(4) { --widget-gradient: linear-gradient(90deg, #3b82f6, #60a5fa); }

.widget:hover {
  transform: translateY(-8px) rotateX(2deg);
  box-shadow: var(--dashboard-shadow-xl), var(--dashboard-shadow-glow);
}

.widget h2 {
  font-size: 1.125rem;
  font-weight: 700;
  color: var(--dashboard-gray-600);
  margin-bottom: 1.5rem;
  text-transform: uppercase;
  letter-spacing: 0.1em;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  position: relative;
}

.widget h2::before {
  content: var(--widget-icon, '📊');
  font-size: 1.5rem;
  padding: 0.5rem;
  background: rgba(14, 165, 233, 0.1);
  border-radius: var(--dashboard-radius-lg);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: var(--dashboard-transition);
}

.widget:nth-child(1) h2::before { --widget-icon: '⚠️'; background: rgba(245, 158, 11, 0.1); }
.widget:nth-child(2) h2::before { --widget-icon: '❌'; background: rgba(239, 68, 68, 0.1); }
.widget:nth-child(3) h2::before { --widget-icon: '✅'; background: rgba(34, 197, 94, 0.1); }
.widget:nth-child(4) h2::before { --widget-icon: '📈'; background: rgba(59, 130, 246, 0.1); }

.widget:hover h2::before {
  transform: scale(1.1) rotate(5deg);
}

.widget p {
  color: var(--dashboard-gray-600);
  margin-bottom: 0.75rem;
  font-size: 1rem;
  font-weight: 500;
  line-height: 1.5;
}

.widget p strong {
  font-weight: 800;
  font-size: 2.5rem;
  background: var(--widget-gradient, var(--dashboard-gradient-primary));
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  display: block;
  margin-top: 0.5rem;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  line-height: 1.2;
  animation: numberCount 1s ease-out 0.5s both;
}

@keyframes numberCount {
  from {
    opacity: 0;
    transform: scale(0.5);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* Enhanced Recent Activity */
.recent-activity {
  background: var(--dashboard-gradient-card);
  backdrop-filter: blur(15px);
  padding: 2.5rem;
  border-radius: var(--dashboard-radius-2xl);
  box-shadow: var(--dashboard-shadow-lg);
  border: 1px solid rgba(255, 255, 255, 0.3);
  position: relative;
  overflow: hidden;
  animation: activitySlideIn 1s ease-out 0.6s both;
}

@keyframes activitySlideIn {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.recent-activity::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #10b981, #34d399);
  border-radius: var(--dashboard-radius-2xl) var(--dashboard-radius-2xl) 0 0;
}

.recent-activity h2 {
  font-size: 1.75rem;
  font-weight: 800;
  background: var(--dashboard-gradient-primary);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  position: relative;
}

.recent-activity h2::before {
  content: '🔄';
  background: var(--dashboard-gradient-primary);
  background-clip: text;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-size: 2rem;
  animation: activityIcon 3s ease-in-out infinite;
}

@keyframes activityIcon {
  0%, 100% { transform: rotate(0deg); }
  50% { transform: rotate(180deg); }
}

.recent-activity ul {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.recent-activity ul li {
  padding: 1.5rem;
  background: linear-gradient(135deg, rgba(248, 250, 252, 0.8) 0%, rgba(241, 245, 249, 0.4) 100%);
  border-radius: var(--dashboard-radius-xl);
  border-left: 4px solid var(--dashboard-primary);
  transition: var(--dashboard-transition);
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  gap: 1rem;
  animation: listItemSlide 0.5s ease-out;
  animation-fill-mode: both;
}

.recent-activity ul li:nth-child(1) { animation-delay: 0.1s; }
.recent-activity ul li:nth-child(2) { animation-delay: 0.2s; }
.recent-activity ul li:nth-child(3) { animation-delay: 0.3s; }
.recent-activity ul li:nth-child(4) { animation-delay: 0.4s; }

@keyframes listItemSlide {
  from {
    opacity: 0;
    transform: translateX(-30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.recent-activity ul li::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(14, 165, 233, 0.1), transparent);
  transition: left 0.5s;
}

.recent-activity ul li:hover::before {
  left: 100%;
}

.recent-activity ul li:hover {
  background: linear-gradient(135deg, var(--dashboard-primary), var(--dashboard-secondary));
  color: var(--dashboard-white);
  transform: translateX(10px) scale(1.02);
  box-shadow: var(--dashboard-shadow-lg);
  border-left-color: var(--dashboard-white);
}

.recent-activity ul li:last-child {
  border-bottom: none;
}

.recent-activity ul li::after {
  content: '🔗';
  position: absolute;
  right: 1.5rem;
  font-size: 1.25rem;
  opacity: 0.6;
  transition: var(--dashboard-transition);
}

.recent-activity ul li:hover::after {
  opacity: 1;
  transform: rotate(45deg);
}

/* Performance indicator */
.performance-indicator {
  position: fixed;
  top: 2rem;
  right: 2rem;
  width: 4px;
  height: 4px;
  background: var(--dashboard-success);
  border-radius: 50%;
  box-shadow: 0 0 10px var(--dashboard-success);
  animation: performancePulse 2s ease-in-out infinite;
  z-index: 1000;
}

@keyframes performancePulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.5; }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .sidebar {
    width: 250px;
    padding: 1.5rem;
  }

  .main-content {
    padding: 2rem;
  }

  .widgets-container {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
  }
}

@media (max-width: 768px) {
  .dashboard-container {
    flex-direction: column;
  }

  .sidebar {
    width: 100%;
    padding: 1rem;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }

  .sidebar .logo {
    font-size: 1.25rem;
    margin-bottom: 0;
    padding: 1rem;
  }

  .sidebar nav ul {
    flex-direction: row;
    gap: 0.5rem;
    overflow-x: auto;
    padding-bottom: 1rem;
  }

  .sidebar nav ul li {
    min-width: 120px;
  }

  .main-content {
    padding: 1rem;
  }

  .main-content header {
    padding: 2rem;
    margin-bottom: 2rem;
  }

  .widgets-container {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .recent-activity {
    padding: 1.5rem;
  }

  .recent-activity ul li {
    flex-direction: column;
    text-align: center;
    gap: 0.5rem;
  }
}

@media (max-width: 480px) {
  .sidebar .logo {
    font-size: 1rem;
    padding: 0.75rem;
  }

  .main-content header h1 {
    font-size: 1.875rem;
  }

  .widget {
    padding: 1.5rem;
  }

  .widget p strong {
    font-size: 2rem;
  }

  .recent-activity {
    padding: 1rem;
  }
}

/* Dark Theme Support */
@media (prefers-color-scheme: dark) {
  .dashboard-container {
    background: linear-gradient(135deg, #0f172a 0%, #1e293b 100%);
  }

  .sidebar {
    background: linear-gradient(180deg, #000000 0%, #0f172a 100%);
  }

  .main-content header,
  .widget,
  .recent-activity {
    background: rgba(15, 23, 42, 0.9);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .widget p,
  .recent-activity ul li {
    color: #e2e8f0;
  }

  .recent-activity ul li {
    background: rgba(30, 41, 59, 0.5);
  }

  .widget h2 {
    color: #94a3b8;
  }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
  .widget,
  .recent-activity,
  .sidebar nav ul li a {
    border: 2px solid currentColor;
  }
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

/* Print Styles */
@media print {
  .dashboard-container {
    background: white;
    color: black;
  }

  .sidebar {
    width: 100%;
    background: white;
    color: black;
    border-bottom: 2px solid #ccc;
  }

  .main-content header,
  .widget,
  .recent-activity {
    background: white;
    box-shadow: none;
    border: 1px solid #ccc;
  }

  .dashboard-container::before {
    display: none;
  }
}

/* Focus states for accessibility */
.sidebar nav ul li a:focus-visible,
.widget:focus-visible,
.recent-activity:focus-visible {
  outline: 3px solid var(--dashboard-primary);
  outline-offset: 3px;
}

/* Loading states */
.widget.loading {
  opacity: 0.7;
  pointer-events: none;
}

.widget.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 30px;
  height: 30px;
  border: 3px solid rgba(14, 165, 233, 0.3);
  border-top: 3px solid var(--dashboard-primary);
  border-radius: 50%;
  animation: widgetSpinner 1s linear infinite;
  transform: translate(-50%, -50%);
}

@keyframes widgetSpinner {
  to { transform: translate(-50%, -50%) rotate(360deg); }
}
import React, { useState, useEffect } from 'react';
import { auth, db } from '../../firebase';
import './History.css';

const History = () => {
  const [predictions, setPredictions] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [filter, setFilter] = useState('all');
  const [dateRange, setDateRange] = useState({
    start: '',
    end: ''
  });

  useEffect(() => {
    const fetchPredictions = async () => {
      setLoading(true);
      try {
        const user = auth.currentUser;
        if (!user) {
          throw new Error("Utilisateur non connecté");
        }

        // Get all predictions from Firestore
        const predictionsCollection = db.collection('types_cables');
        const querySnapshot = await predictionsCollection.get();
        
        const allPredictions = [];
        querySnapshot.forEach(doc => {
          const data = doc.data();
          const date = doc.id; // Date is the document ID
          
          // Add each prediction type as a separate entry
          if (data.cable_calage > 0) {
            for (let i = 0; i < data.cable_calage; i++) {
              allPredictions.push({
                date,
                type: 'cable_calage',
                timestamp: data.lastUpdated ? data.lastUpdated.toDate() : new Date()
              });
            }
          }
          
          if (data.cable_noo > 0) {
            for (let i = 0; i < data.cable_noo; i++) {
              allPredictions.push({
                date,
                type: 'cable_noo',
                timestamp: data.lastUpdated ? data.lastUpdated.toDate() : new Date()
              });
            }
          }
          
          if (data.cable_parfait > 0) {
            for (let i = 0; i < data.cable_parfait; i++) {
              allPredictions.push({
                date,
                type: 'cable_parfait',
                timestamp: data.lastUpdated ? data.lastUpdated.toDate() : new Date()
              });
            }
          }
        });
        
        // Sort by timestamp (most recent first)
        allPredictions.sort((a, b) => b.timestamp - a.timestamp);
        
        setPredictions(allPredictions);
      } catch (err) {
        console.error("Error fetching predictions:", err);
        setError("Erreur lors de la récupération de l'historique des prédictions.");
      } finally {
        setLoading(false);
      }
    };

    fetchPredictions();
  }, []);

  const handleFilterChange = (e) => {
    setFilter(e.target.value);
  };

  const handleDateChange = (e) => {
    setDateRange({
      ...dateRange,
      [e.target.name]: e.target.value
    });
  };

  const getFilteredPredictions = () => {
    return predictions.filter(prediction => {
      // Filter by type
      if (filter !== 'all' && prediction.type !== filter) {
        return false;
      }
      
      // Filter by date range
      if (dateRange.start && new Date(prediction.date.split('-').reverse().join('-')) < new Date(dateRange.start)) {
        return false;
      }
      
      if (dateRange.end && new Date(prediction.date.split('-').reverse().join('-')) > new Date(dateRange.end)) {
        return false;
      }
      
      return true;
    });
  };

  const getTypeLabel = (type) => {
    switch (type) {
      case 'cable_calage': return 'Câble Calage';
      case 'cable_noo': return 'Câble Noo';
      case 'cable_parfait': return 'Câble Parfait';
      default: return type;
    }
  };

  const filteredPredictions = getFilteredPredictions();

  if (loading) {
    return <div className="history-loading">Chargement de l'historique...</div>;
  }

  return (
    <div className="history-container">
      <div className="history-header">
        <h2>Historique des Classifications</h2>
        
        <div className="history-filters">
          <div className="filter-group">
            <label>Type de câble:</label>
            <select value={filter} onChange={handleFilterChange}>
              <option value="all">Tous les types</option>
              <option value="cable_calage">Câble Calage</option>
              <option value="cable_noo">Câble Noo</option>
              <option value="cable_parfait">Câble Parfait</option>
            </select>
          </div>
          
          <div className="filter-group">
            <label>Date de début:</label>
            <input 
              type="date" 
              name="start" 
              value={dateRange.start} 
              onChange={handleDateChange}
            />
          </div>
          
          <div className="filter-group">
            <label>Date de fin:</label>
            <input 
              type="date" 
              name="end" 
              value={dateRange.end} 
              onChange={handleDateChange}
            />
          </div>
        </div>
      </div>

      {error && <div className="history-error">{error}</div>}

      {filteredPredictions.length > 0 ? (
        <div className="history-table-container">
          <table className="history-table">
            <thead>
              <tr>
                <th>Date</th>
                <th>Heure</th>
                <th>Type de câble</th>
              </tr>
            </thead>
            <tbody>
              {filteredPredictions.map((prediction, index) => (
                <tr key={index}>
                  <td>{prediction.date}</td>
                  <td>{prediction.timestamp.toLocaleTimeString()}</td>
                  <td>
                    <span className={`prediction-type ${prediction.type}`}>
                      {getTypeLabel(prediction.type)}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      ) : (
        <div className="history-empty">
          Aucune prédiction trouvée pour les critères sélectionnés.
        </div>
      )}
    </div>
  );
};

export default History;

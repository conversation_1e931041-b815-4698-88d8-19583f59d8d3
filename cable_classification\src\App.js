import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { useEffect, useState } from 'react';
import { auth } from './firebase';

import Classification from './Components/Classification';
import WebcamClassifier from './Components/WebcamClassifier';
import GeminiWebcam from './Components/GeminiWebcam';
import './App.css';
import Login from './Components/Auth/Login/Login';
import SignUp from './Components/Auth/SignUp/SignUp';
import Admin from './Components/AdminDashboard/AdminDash';
import AdminK from './Components/AdminDashboard/Admin';
import Profile from './Components/Profile/Profile';
import History from './Components/History/History';
import Home from './Components/Home/Home';
import NotFound from './Components/NotFound/NotFound';
import Navigation from './Components/Navigation/Navigation';

function App() {
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Check authentication state when app loads
    const unsubscribe = auth.onAuthStateChanged(() => {
      setLoading(false);
    });

    return () => unsubscribe();
  }, []);

  if (loading) {
    return <div className="app-loading">Chargement...</div>;
  }

  return (
    <Router>
      <Navigation />
      <div className="app-container">
        <Routes>
          <Route path="/" element={<Login />} />
          <Route path="/SignUp" element={<SignUp />} />

          {/* Home Page */}
          <Route path="/Home" element={<Home />} />

          {/* Employee Routes */}
          <Route path="/Classification" element={<Classification />} />
          <Route path="/WebcamClassifier" element={<WebcamClassifier />} />
          <Route path="/GeminiWebcam" element={<GeminiWebcam />} />
          <Route path="/History" element={<History />} />

          {/* Admin Routes */}
          <Route path="/Admin" element={<Admin />} />
          <Route path="/AdminK" element={<AdminK />} />

          {/* Common Routes */}
          <Route path="/Profile" element={<Profile />} />

          {/* 404 Route */}
          <Route path="*" element={<NotFound />} />
        </Routes>
      </div>
    </Router>
  );
}

export default App;

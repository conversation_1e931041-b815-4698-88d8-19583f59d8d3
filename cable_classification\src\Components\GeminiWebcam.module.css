/* Container principal */
.container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

/* Header */
.header {
  text-align: center;
  margin-bottom: 30px;
  padding: 40px;
  background: rgba(255, 255, 255, 0.95);
  color: #333;
  border-radius: 25px;
  box-shadow: 0 15px 35px rgba(0,0,0,0.1);
  backdrop-filter: blur(10px);
}

.header h1 {
  font-size: 3rem;
  margin-bottom: 15px;
  font-weight: 800;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.header p {
  font-size: 1.2rem;
  opacity: 0.8;
  margin: 0;
  font-weight: 500;
}

/* Main content */
.mainContent {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 25px;
  padding: 40px;
  box-shadow: 0 15px 35px rgba(0,0,0,0.1);
  backdrop-filter: blur(10px);
}

/* Webcam container */
.webcamContainer {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 40px;
  margin-bottom: 30px;
}

/* Video section */
.videoSection {
  text-align: center;
}

.videoWrapper {
  position: relative;
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 15px 35px rgba(0,0,0,0.2);
  margin-bottom: 25px;
  background: #000;
  border: 3px solid transparent;
  background-clip: padding-box;
}

.videoWrapper::before {
  content: '';
  position: absolute;
  top: -3px;
  left: -3px;
  right: -3px;
  bottom: -3px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 23px;
  z-index: -1;
}

.webcam {
  width: 100%;
  height: auto;
  display: block;
  max-height: 450px;
  object-fit: cover;
}

.videoOverlay {
  position: absolute;
  top: 15px;
  left: 15px;
  right: 15px;
  background: rgba(0,0,0,0.8);
  color: white;
  padding: 15px;
  border-radius: 15px;
  font-size: 1rem;
  display: flex;
  justify-content: space-between;
  font-weight: 600;
  backdrop-filter: blur(10px);
}

/* Controls */
.controls {
  display: flex;
  gap: 15px;
  justify-content: center;
  flex-wrap: wrap;
}

.btn {
  padding: 15px 30px;
  border: none;
  border-radius: 30px;
  font-size: 1rem;
  font-weight: 700;
  cursor: pointer;
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 1px;
  display: flex;
  align-items: center;
  gap: 10px;
  box-shadow: 0 5px 15px rgba(0,0,0,0.2);
}

.btnPrimary {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.btnSuccess {
  background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
  color: white;
}

.btnWarning {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  color: white;
}

.btnDanger {
  background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
  color: white;
}

.btnInfo {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
}

.btn:hover {
  transform: translateY(-3px);
  box-shadow: 0 10px 25px rgba(0,0,0,0.3);
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* Results section */
.resultsSection {
  display: grid;
  grid-template-columns: 1fr;
  gap: 20px;
}

.resultCard {
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 20px;
  padding: 30px;
  box-shadow: 0 10px 25px rgba(0,0,0,0.1);
  height: fit-content;
  border: 2px solid transparent;
  background-clip: padding-box;
}

.resultCard::before {
  content: '';
  position: absolute;
  top: -2px;
  left: -2px;
  right: -2px;
  bottom: -2px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 22px;
  z-index: -1;
}

.resultCard h3 {
  margin-bottom: 25px;
  color: #333;
  font-size: 1.5rem;
  display: flex;
  align-items: center;
  gap: 15px;
  font-weight: 700;
}

/* Status indicators */
.statusIndicator {
  width: 15px;
  height: 15px;
  border-radius: 50%;
  background: #ddd;
}

.statusIndicator.active {
  background: #28a745;
  animation: pulse 2s infinite;
}

.statusIndicator.processing {
  background: #ffc107;
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.7; transform: scale(1.1); }
  100% { opacity: 1; transform: scale(1); }
}

/* Live indicator */
.liveIndicator {
  background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
  color: white;
  padding: 5px 12px;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: bold;
  margin-left: 15px;
  animation: pulse 1.5s infinite;
  box-shadow: 0 3px 10px rgba(255, 65, 108, 0.3);
}

/* Predictions */
.prediction {
  display: flex;
  align-items: center;
  gap: 20px;
  margin-bottom: 20px;
  padding: 20px;
  border-radius: 15px;
  font-weight: 700;
  font-size: 1.1rem;
}

.prediction.cablenormal {
  background: linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%);
  color: #155724;
  border-left: 5px solid #28a745;
}

.prediction.cableredresse {
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  color: #856404;
  border-left: 5px solid #ffc107;
}

.prediction.cabledefeaut {
  background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
  color: #721c24;
  border-left: 5px solid #dc3545;
}

.predictionIcon {
  display: flex;
  align-items: center;
}

.confidence {
  font-size: 1.4rem;
  margin-top: 8px;
  font-weight: 800;
}

/* Probabilities */
.probabilities {
  margin-top: 20px;
}

.probabilities h4 {
  margin-bottom: 15px;
  color: #333;
  font-size: 1.1rem;
  font-weight: 700;
}

.probabilityBar {
  display: flex;
  align-items: center;
  margin-bottom: 12px;
  font-size: 0.95rem;
}

.probabilityLabel {
  width: 130px;
  font-weight: 600;
  font-size: 0.9rem;
}

.probabilityValue {
  flex: 1;
  height: 10px;
  background: #e9ecef;
  border-radius: 5px;
  margin: 0 15px;
  overflow: hidden;
  position: relative;
}

.probabilityFill {
  height: 100%;
  border-radius: 5px;
  transition: width 0.5s ease;
}

.probabilityText {
  width: 60px;
  text-align: right;
  font-weight: 700;
  font-size: 0.9rem;
}

/* Gemini analysis */
.geminiAnalysis {
  margin-top: 25px;
  padding: 20px;
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  border-radius: 15px;
  border-left: 5px solid #2196f3;
}

.geminiAnalysis h4 {
  color: #1976d2;
  margin-bottom: 15px;
  font-size: 1.1rem;
  font-weight: 700;
}

.geminiAnalysis p {
  margin-bottom: 10px;
  line-height: 1.6;
  font-size: 0.95rem;
  font-weight: 500;
}

/* Waiting state */
.waitingState {
  text-align: center;
  padding: 40px 20px;
  color: #666;
}

.waitingState p {
  margin-bottom: 15px;
  font-size: 1.1rem;
  font-weight: 500;
}

/* Performance stats */
.performanceStats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
  gap: 20px;
  margin-top: 30px;
}

.statCard {
  background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
  padding: 25px;
  border-radius: 20px;
  text-align: center;
  box-shadow: 0 10px 25px rgba(0,0,0,0.1);
  border: 2px solid #e9ecef;
  transition: all 0.3s ease;
}

.statCard:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0,0,0,0.15);
  border-color: #667eea;
}

.statValue {
  font-size: 2.5rem;
  font-weight: 800;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  margin-bottom: 8px;
}

.statLabel {
  font-size: 1rem;
  color: #666;
  font-weight: 600;
}

/* Error message */
.errorMessage {
  position: fixed;
  bottom: 20px;
  right: 20px;
  background: linear-gradient(135deg, #dc3545, #c82333);
  color: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(220, 53, 69, 0.4);
  z-index: 1000;
  max-width: 450px;
  animation: slideInRight 0.3s ease-out;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.errorHeader {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 20px 10px;
  font-weight: 600;
  font-size: 16px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.errorClose {
  background: none;
  border: none;
  color: white;
  font-size: 18px;
  cursor: pointer;
  padding: 0;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: background-color 0.2s;
}

.errorClose:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.errorContent {
  padding: 10px 20px;
  font-size: 14px;
  line-height: 1.4;
}

.errorActions {
  padding: 10px 20px 15px;
  display: flex;
  justify-content: flex-end;
}

.errorActions .btn {
  font-size: 12px;
  padding: 6px 12px;
  margin: 0;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* Responsive design */
@media (max-width: 768px) {
  .webcamContainer {
    grid-template-columns: 1fr;
  }

  .header h1 {
    font-size: 2.5rem;
  }

  .controls {
    flex-direction: column;
    align-items: center;
  }

  .btn {
    width: 250px;
    justify-content: center;
  }

  .performanceStats {
    grid-template-columns: repeat(2, 1fr);
  }

  .container {
    padding: 15px;
  }

  .mainContent {
    padding: 25px;
  }
}

@media (max-width: 480px) {
  .performanceStats {
    grid-template-columns: 1fr;
  }

  .probabilityLabel {
    width: 90px;
    font-size: 0.8rem;
  }

  .probabilityText {
    width: 50px;
    font-size: 0.8rem;
  }

  .header h1 {
    font-size: 2rem;
  }
}

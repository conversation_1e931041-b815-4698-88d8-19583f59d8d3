import React, { useState } from 'react';
import './ModelPerformance.css';

const ModelPerformance = () => {
  const [modelMetrics, setModelMetrics] = useState({
    accuracy: 0.92,
    precision: 0.89,
    recall: 0.94,
    f1Score: 0.91,
    confusionMatrix: [
      [156, 14],
      [21, 179]
    ],
    classNames: ['Cable Damage Type 1', 'Cable Damage Type 2'],
    trainingHistory: [
      { epoch: 1, accuracy: 0.65, loss: 0.82, val_accuracy: 0.63, val_loss: 0.85 },
      { epoch: 2, accuracy: 0.72, loss: 0.68, val_accuracy: 0.70, val_loss: 0.72 },
      { epoch: 3, accuracy: 0.78, loss: 0.55, val_accuracy: 0.75, val_loss: 0.62 },
      { epoch: 4, accuracy: 0.83, loss: 0.43, val_accuracy: 0.79, val_loss: 0.52 },
      { epoch: 5, accuracy: 0.87, loss: 0.35, val_accuracy: 0.82, val_loss: 0.45 },
      { epoch: 6, accuracy: 0.89, loss: 0.29, val_accuracy: 0.85, val_loss: 0.38 },
      { epoch: 7, accuracy: 0.91, loss: 0.24, val_accuracy: 0.87, val_loss: 0.33 },
      { epoch: 8, accuracy: 0.92, loss: 0.21, val_accuracy: 0.89, val_loss: 0.29 },
      { epoch: 9, accuracy: 0.93, loss: 0.18, val_accuracy: 0.90, val_loss: 0.26 },
      { epoch: 10, accuracy: 0.94, loss: 0.16, val_accuracy: 0.92, val_loss: 0.23 }
    ]
  });

  const [selectedModel, setSelectedModel] = useState('efficientnet_b3');
  
  const models = [
    { id: 'efficientnet_b3', name: 'EfficientNet B3', date: '2023-05-15' },
    { id: 'efficientnet_b4', name: 'EfficientNet B4', date: '2023-05-20' },
    { id: 'cnn_model', name: 'CNN Model', date: '2023-04-10' }
  ];
  
  const handleModelChange = (e) => {
    setSelectedModel(e.target.value);
    // In a real app, you would fetch the metrics for the selected model here
  };
  
  return (
    <div className="model-performance">
      <h2>Model Performance</h2>
      
      <div className="model-selector">
        <label htmlFor="model-select">Select Model:</label>
        <select 
          id="model-select" 
          value={selectedModel} 
          onChange={handleModelChange}
          className="model-select"
        >
          {models.map(model => (
            <option key={model.id} value={model.id}>
              {model.name} (Trained: {model.date})
            </option>
          ))}
        </select>
      </div>
      
      <div className="metrics-overview">
        <h3>Performance Metrics</h3>
        <div className="metrics-grid">
          <div className="metric-card">
            <div className="metric-value">{(modelMetrics.accuracy * 100).toFixed(1)}%</div>
            <div className="metric-label">Accuracy</div>
          </div>
          <div className="metric-card">
            <div className="metric-value">{(modelMetrics.precision * 100).toFixed(1)}%</div>
            <div className="metric-label">Precision</div>
          </div>
          <div className="metric-card">
            <div className="metric-value">{(modelMetrics.recall * 100).toFixed(1)}%</div>
            <div className="metric-label">Recall</div>
          </div>
          <div className="metric-card">
            <div className="metric-value">{(modelMetrics.f1Score * 100).toFixed(1)}%</div>
            <div className="metric-label">F1 Score</div>
          </div>
        </div>
      </div>
      
      <div className="confusion-matrix">
        <h3>Confusion Matrix</h3>
        <div className="matrix-container">
          <div className="matrix-labels">
            <div className="matrix-axis-label">Predicted</div>
            <div className="matrix-axis-label vertical">Actual</div>
          </div>
          <table className="matrix-table">
            <thead>
              <tr>
                <th></th>
                {modelMetrics.classNames.map((className, index) => (
                  <th key={index}>{className}</th>
                ))}
              </tr>
            </thead>
            <tbody>
              {modelMetrics.confusionMatrix.map((row, rowIndex) => (
                <tr key={rowIndex}>
                  <th>{modelMetrics.classNames[rowIndex]}</th>
                  {row.map((cell, cellIndex) => (
                    <td 
                      key={cellIndex} 
                      className={rowIndex === cellIndex ? 'correct' : 'incorrect'}
                    >
                      {cell}
                    </td>
                  ))}
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>
      
      <div className="training-history">
        <h3>Training History</h3>
        <div className="history-chart">
          {/* In a real app, you would use a charting library like Chart.js or Recharts */}
          <div className="chart-placeholder">
            <div className="chart-lines">
              {modelMetrics.trainingHistory.map((epoch, index) => (
                <div 
                  key={index} 
                  className="chart-line accuracy" 
                  style={{ 
                    left: `${(index / (modelMetrics.trainingHistory.length - 1)) * 100}%`,
                    height: `${epoch.accuracy * 100}%` 
                  }}
                  title={`Epoch ${epoch.epoch}: Accuracy ${(epoch.accuracy * 100).toFixed(1)}%`}
                >
                  <div className="chart-point"></div>
                </div>
              ))}
              {modelMetrics.trainingHistory.map((epoch, index) => (
                <div 
                  key={index} 
                  className="chart-line validation" 
                  style={{ 
                    left: `${(index / (modelMetrics.trainingHistory.length - 1)) * 100}%`,
                    height: `${epoch.val_accuracy * 100}%` 
                  }}
                  title={`Epoch ${epoch.epoch}: Validation Accuracy ${(epoch.val_accuracy * 100).toFixed(1)}%`}
                >
                  <div className="chart-point"></div>
                </div>
              ))}
            </div>
            <div className="chart-legend">
              <div className="legend-item">
                <div className="legend-color accuracy"></div>
                <div className="legend-label">Training Accuracy</div>
              </div>
              <div className="legend-item">
                <div className="legend-color validation"></div>
                <div className="legend-label">Validation Accuracy</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <div className="model-actions">
        <button className="action-button">Download Model</button>
        <button className="action-button">Export Metrics</button>
        <button className="action-button">Compare Models</button>
      </div>
    </div>
  );
};

export default ModelPerformance;

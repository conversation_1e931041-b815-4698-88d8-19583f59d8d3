{"name": "cable_classification", "version": "0.1.0", "private": true, "dependencies": {"@tensorflow/tfjs": "^4.10.0", "@tensorflow/tfjs-tflite": "^0.0.1-alpha.10", "@testing-library/dom": "^9.3.1", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "firebase": "^10.5.0", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.16.0", "react-scripts": "5.0.1", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}